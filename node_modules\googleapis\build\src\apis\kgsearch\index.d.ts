/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { kgsearch_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof kgsearch_v1.Kgsearch;
};
export declare function kgsearch(version: 'v1'): kgsearch_v1.Kgsearch;
export declare function kgsearch(options: kgsearch_v1.Options): kgsearch_v1.Kgsearch;
declare const auth: AuthPlus;
export { auth };
export { kgsearch_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
