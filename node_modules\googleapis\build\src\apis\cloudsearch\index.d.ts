/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { cloudsearch_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof cloudsearch_v1.Cloudsearch;
};
export declare function cloudsearch(version: 'v1'): cloudsearch_v1.Cloudsearch;
export declare function cloudsearch(options: cloudsearch_v1.Options): cloudsearch_v1.Cloudsearch;
declare const auth: AuthPlus;
export { auth };
export { cloudsearch_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
