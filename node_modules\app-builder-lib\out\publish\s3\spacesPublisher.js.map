{"version": 3, "file": "spacesPublisher.js", "sourceRoot": "", "sources": ["../../../src/publish/s3/spacesPublisher.ts"], "names": [], "mappings": ";;AAAA,+CAAyE;AAGzE,uDAAmD;AAEnD,MAAqB,eAAgB,SAAQ,iCAAe;IAG1D,YACE,OAAuB,EACN,IAAmB;QAEpC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QAFH,SAAI,GAAJ,IAAI,CAAe;QAJ7B,iBAAY,GAAG,QAAQ,CAAA;IAOhC,CAAC;IAED,6DAA6D;IAC7D,MAAM,CAAC,sBAAsB,CAAC,OAAsB,EAAE,qBAAoC,EAAE,aAAsB;QAChH,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,IAAI,wCAAyB,CAAC,4GAA4G,CAAC,CAAA;QACnJ,CAAC;QACD,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,IAAI,wCAAyB,CAAC,8GAA8G,CAAC,CAAA;QACrJ,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,IAAI,qBAAqB,IAAI,IAAI,EAAE,CAAC;YAC7D,OAAO,CAAC,OAAO,GAAG,qBAAqB,CAAA;QACzC,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAES,aAAa;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;IACvB,CAAC;IAES,kBAAkB,CAAC,IAAmB;QAC9C,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;QAE9B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,yBAAyB,CAAC,CAAA;QACrE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAEvC,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAA;QACvC,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAA;QAC3C,IAAI,IAAA,8BAAe,EAAC,SAAS,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,wCAAyB,CAAC,iFAAiF,CAAC,CAAA;QACxH,CAAC;QACD,IAAI,IAAA,8BAAe,EAAC,SAAS,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,wCAAyB,CAAC,qFAAqF,CAAC,CAAA;QAC5H,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;IACrC,CAAC;CACF;AA9CD,kCA8CC", "sourcesContent": ["import { InvalidConfigurationError, isEmptyOrSpaces } from \"builder-util\"\nimport { SpacesOptions } from \"builder-util-runtime\"\nimport { PublishContext } from \"electron-publish\"\nimport { BaseS3Publisher } from \"./BaseS3Publisher\"\n\nexport default class SpacesPublisher extends BaseS3Publisher {\n  readonly providerName = \"spaces\"\n\n  constructor(\n    context: PublishContext,\n    private readonly info: SpacesOptions\n  ) {\n    super(context, info)\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  static checkAndResolveOptions(options: SpacesOptions, channelFromAppVersion: string | null, errorIfCannot: boolean) {\n    if (options.name == null) {\n      throw new InvalidConfigurationError(`Please specify \"name\" for \"spaces\" publish provider (see https://www.electron.build/publish#spacesoptions)`)\n    }\n    if (options.region == null) {\n      throw new InvalidConfigurationError(`Please specify \"region\" for \"spaces\" publish provider (see https://www.electron.build/publish#spacesoptions)`)\n    }\n\n    if (options.channel == null && channelFromAppVersion != null) {\n      options.channel = channelFromAppVersion\n    }\n    return Promise.resolve()\n  }\n\n  protected getBucketName(): string {\n    return this.info.name\n  }\n\n  protected configureS3Options(args: Array<string>): void {\n    super.configureS3Options(args)\n\n    args.push(\"--endpoint\", `${this.info.region}.digitaloceanspaces.com`)\n    args.push(\"--region\", this.info.region)\n\n    const accessKey = process.env.DO_KEY_ID\n    const secretKey = process.env.DO_SECRET_KEY\n    if (isEmptyOrSpaces(accessKey)) {\n      throw new InvalidConfigurationError(\"Please set env DO_KEY_ID (see https://www.electron.build/publish#spacesoptions)\")\n    }\n    if (isEmptyOrSpaces(secretKey)) {\n      throw new InvalidConfigurationError(\"Please set env DO_SECRET_KEY (see https://www.electron.build/publish#spacesoptions)\")\n    }\n    args.push(\"--accessKey\", accessKey)\n    args.push(\"--secretKey\", secretKey)\n  }\n}\n"]}