import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import crypto from 'crypto';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class AccountManager {
  constructor() {
    this.accountsFile = join(__dirname, 'accounts.json');
    this.credentialsFile = join(__dirname, 'credentials.json');
    this.encryptionKey = process.env.ENCRYPTION_KEY || 'default-key-change-this';
    this.accounts = this.loadAccounts();
    this.credentials = this.loadCredentials();
  }

  // Encryption/Decryption for sensitive data
  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }

  decrypt(encryptedText) {
    try {
      const parts = encryptedText.split(':');
      if (parts.length !== 2) {
        // Fallback for old encryption format
        const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
        let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
      }

      const iv = Buffer.from(parts[0], 'hex');
      const encrypted = parts[1];
      const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      return null;
    }
  }

  // Load accounts.json (current active account info)
  loadAccounts() {
    if (!existsSync(this.accountsFile)) {
      const defaultAccounts = {
        activeAccount: null,
        lastUpdated: new Date().toISOString(),
        accounts: []
      };
      this.saveAccounts(defaultAccounts);
      return defaultAccounts;
    }

    try {
      const data = readFileSync(this.accountsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error loading accounts:', error);
      return { activeAccount: null, lastUpdated: new Date().toISOString(), accounts: [] };
    }
  }

  // Load credentials.json (encrypted credentials for all accounts)
  loadCredentials() {
    if (!existsSync(this.credentialsFile)) {
      return {};
    }

    try {
      const data = readFileSync(this.credentialsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error loading credentials:', error);
      return {};
    }
  }

  // Save accounts.json
  saveAccounts(accounts = this.accounts) {
    try {
      accounts.lastUpdated = new Date().toISOString();
      writeFileSync(this.accountsFile, JSON.stringify(accounts, null, 2));
      this.accounts = accounts;
      return true;
    } catch (error) {
      console.error('Error saving accounts:', error);
      return false;
    }
  }

  // Save credentials.json
  saveCredentials(credentials = this.credentials) {
    try {
      writeFileSync(this.credentialsFile, JSON.stringify(credentials, null, 2));
      this.credentials = credentials;
      return true;
    } catch (error) {
      console.error('Error saving credentials:', error);
      return false;
    }
  }

  // Add a new Google account
  addAccount(accountData) {
    const {
      email,
      name,
      picture,
      accessToken,
      refreshToken,
      expiresAt,
      scope
    } = accountData;

    const accountId = this.generateAccountId(email);
    
    // Add to accounts list (public info)
    const accountInfo = {
      id: accountId,
      email: email,
      name: name,
      picture: picture,
      addedAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
      isActive: false,
      quotaUsed: 0,
      quotaLimit: 1000, // Google login free tier
      requestsToday: 0
    };

    // Remove existing account with same email
    this.accounts.accounts = this.accounts.accounts.filter(acc => acc.email !== email);
    this.accounts.accounts.push(accountInfo);

    // Add encrypted credentials (sensitive info)
    this.credentials[accountId] = {
      email: email,
      accessToken: this.encrypt(accessToken),
      refreshToken: this.encrypt(refreshToken),
      expiresAt: expiresAt,
      scope: scope,
      createdAt: new Date().toISOString()
    };

    // If this is the first account, make it active
    if (!this.accounts.activeAccount) {
      this.setActiveAccount(accountId);
    }

    this.saveAccounts();
    this.saveCredentials();

    return accountId;
  }

  // Set active account
  setActiveAccount(accountId) {
    // Deactivate all accounts
    this.accounts.accounts.forEach(acc => acc.isActive = false);
    
    // Activate the selected account
    const account = this.accounts.accounts.find(acc => acc.id === accountId);
    if (account) {
      account.isActive = true;
      account.lastUsed = new Date().toISOString();
      this.accounts.activeAccount = accountId;
      this.saveAccounts();
      return true;
    }
    return false;
  }

  // Get active account info
  getActiveAccount() {
    if (!this.accounts.activeAccount) return null;
    
    const account = this.accounts.accounts.find(acc => acc.id === this.accounts.activeAccount);
    if (!account) return null;

    const credentials = this.credentials[this.accounts.activeAccount];
    if (!credentials) return null;

    return {
      ...account,
      accessToken: this.decrypt(credentials.accessToken),
      refreshToken: this.decrypt(credentials.refreshToken),
      expiresAt: credentials.expiresAt,
      scope: credentials.scope
    };
  }

  // Get all accounts (without sensitive data)
  getAllAccounts() {
    return this.accounts.accounts.map(acc => ({
      ...acc,
      hasCredentials: !!this.credentials[acc.id]
    }));
  }

  // Remove account
  removeAccount(accountId) {
    this.accounts.accounts = this.accounts.accounts.filter(acc => acc.id !== accountId);
    delete this.credentials[accountId];

    // If removed account was active, set first available as active
    if (this.accounts.activeAccount === accountId) {
      this.accounts.activeAccount = null;
      if (this.accounts.accounts.length > 0) {
        this.setActiveAccount(this.accounts.accounts[0].id);
      }
    }

    this.saveAccounts();
    this.saveCredentials();
    return true;
  }

  // Update account credentials (for token refresh)
  updateAccountCredentials(accountId, newCredentials) {
    if (!this.credentials[accountId]) return false;

    const { accessToken, refreshToken, expiresAt } = newCredentials;
    
    this.credentials[accountId] = {
      ...this.credentials[accountId],
      accessToken: this.encrypt(accessToken),
      refreshToken: refreshToken ? this.encrypt(refreshToken) : this.credentials[accountId].refreshToken,
      expiresAt: expiresAt
    };

    this.saveCredentials();
    return true;
  }

  // Update quota usage
  updateQuotaUsage(accountId, requestsUsed = 1) {
    const account = this.accounts.accounts.find(acc => acc.id === accountId);
    if (!account) return false;

    account.quotaUsed += requestsUsed;
    account.requestsToday += requestsUsed;
    account.lastUsed = new Date().toISOString();

    this.saveAccounts();
    return true;
  }

  // Reset daily quota (call this daily)
  resetDailyQuota() {
    this.accounts.accounts.forEach(acc => {
      acc.requestsToday = 0;
    });
    this.saveAccounts();
  }

  // Check if account can make requests
  canMakeRequest(accountId) {
    const account = this.accounts.accounts.find(acc => acc.id === accountId);
    if (!account) return false;

    return account.requestsToday < account.quotaLimit;
  }

  // Get account with lowest usage (for load balancing)
  getBestAvailableAccount() {
    const availableAccounts = this.accounts.accounts.filter(acc => 
      this.canMakeRequest(acc.id) && this.credentials[acc.id]
    );

    if (availableAccounts.length === 0) return null;

    // Sort by usage (lowest first)
    availableAccounts.sort((a, b) => a.requestsToday - b.requestsToday);
    
    const bestAccount = availableAccounts[0];
    const credentials = this.credentials[bestAccount.id];

    return {
      ...bestAccount,
      accessToken: this.decrypt(credentials.accessToken),
      refreshToken: this.decrypt(credentials.refreshToken),
      expiresAt: credentials.expiresAt,
      scope: credentials.scope
    };
  }

  // Generate unique account ID
  generateAccountId(email) {
    return crypto.createHash('md5').update(email + Date.now()).digest('hex').substring(0, 16);
  }

  // Get account statistics
  getAccountStats() {
    const totalAccounts = this.accounts.accounts.length;
    const activeAccount = this.getActiveAccount();
    const totalQuotaUsed = this.accounts.accounts.reduce((sum, acc) => sum + acc.requestsToday, 0);
    const totalQuotaLimit = this.accounts.accounts.reduce((sum, acc) => sum + acc.quotaLimit, 0);

    return {
      totalAccounts,
      activeAccount: activeAccount ? {
        email: activeAccount.email,
        name: activeAccount.name,
        quotaUsed: activeAccount.requestsToday,
        quotaLimit: activeAccount.quotaLimit
      } : null,
      totalQuotaUsed,
      totalQuotaLimit,
      quotaPercentage: totalQuotaLimit > 0 ? (totalQuotaUsed / totalQuotaLimit) * 100 : 0
    };
  }
}

export default AccountManager;
