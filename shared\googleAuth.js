import { google } from 'googleapis';
import { createServer } from 'http';
import { parse } from 'url';
import open from 'open';

class GoogleAuthService {
  constructor() {
    // Use the same OAuth credentials as gemini-cli-main for compatibility
    this.clientId = process.env.GOOGLE_CLIENT_ID || '681255809395-oo8ft2oprdrnp9e3aqf6av3hmdib135j.apps.googleusercontent.com';
    this.clientSecret = process.env.GOOGLE_CLIENT_SECRET || '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';
    this.redirectUri = 'http://localhost:3333/oauth/callback';

    this.oauth2Client = new google.auth.OAuth2(
      this.clientId,
      this.clientSecret,
      this.redirectUri
    );

    // Use the same scopes as gemini-cli-main for full compatibility
    this.scopes = [
      'https://www.googleapis.com/auth/cloud-platform',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile'
    ];
  }

  // Generate OAuth URL
  generateAuthUrl() {
    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: this.scopes,
      prompt: 'consent', // Force consent to get refresh token
      include_granted_scopes: true
    });
  }

  // Start OAuth flow
  async startAuthFlow() {
    return new Promise((resolve, reject) => {
      const server = createServer(async (req, res) => {
        const url = parse(req.url, true);
        
        if (url.pathname === '/oauth/callback') {
          const { code, error } = url.query;
          
          if (error) {
            res.writeHead(400, { 'Content-Type': 'text/html' });
            res.end(`
              <html>
                <body>
                  <h1>Authentication Failed</h1>
                  <p>Error: ${error}</p>
                  <p>You can close this window.</p>
                </body>
              </html>
            `);
            server.close();
            reject(new Error(`OAuth error: ${error}`));
            return;
          }

          if (code) {
            try {
              console.log('Exchanging code for tokens...');

              // Exchange code for tokens
              const tokenResponse = await this.oauth2Client.getToken(code);
              console.log('Token response:', tokenResponse);

              if (!tokenResponse || !tokenResponse.tokens) {
                throw new Error('No tokens received from Google OAuth');
              }

              const { tokens } = tokenResponse;
              console.log('Tokens received:', {
                hasAccessToken: !!tokens.access_token,
                hasRefreshToken: !!tokens.refresh_token,
                expiresAt: tokens.expiry_date
              });

              if (!tokens.access_token) {
                throw new Error('No access token received');
              }

              this.oauth2Client.setCredentials(tokens);

              // Get user info
              console.log('Getting user info...');
              const oauth2 = google.oauth2({ version: 'v2', auth: this.oauth2Client });
              const userInfo = await oauth2.userinfo.get();
              console.log('User info received:', userInfo.data.email);

              const result = {
                email: userInfo.data.email,
                name: userInfo.data.name,
                picture: userInfo.data.picture,
                accessToken: tokens.access_token,
                refreshToken: tokens.refresh_token,
                expiresAt: tokens.expiry_date,
                scope: tokens.scope
              };

              res.writeHead(200, { 'Content-Type': 'text/html' });
              res.end(`
                <html>
                  <body>
                    <h1>Authentication Successful!</h1>
                    <p>Welcome, ${result.name}!</p>
                    <p>Email: ${result.email}</p>
                    <p>You can close this window and return to the application.</p>
                    <script>
                      setTimeout(() => window.close(), 3000);
                    </script>
                  </body>
                </html>
              `);

              server.close();
              resolve(result);
            } catch (error) {
              res.writeHead(500, { 'Content-Type': 'text/html' });
              res.end(`
                <html>
                  <body>
                    <h1>Authentication Error</h1>
                    <p>Failed to complete authentication: ${error.message}</p>
                    <p>You can close this window.</p>
                  </body>
                </html>
              `);
              server.close();
              reject(error);
            }
          }
        } else {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('Not Found');
        }
      });

      server.listen(3333, () => {
        console.log('OAuth server started on http://localhost:3333');
        const authUrl = this.generateAuthUrl();
        console.log('Opening browser for authentication...');
        open(authUrl);
      });

      // Timeout after 5 minutes
      setTimeout(() => {
        server.close();
        reject(new Error('Authentication timeout'));
      }, 5 * 60 * 1000);
    });
  }

  // Refresh access token
  async refreshAccessToken(refreshToken) {
    try {
      this.oauth2Client.setCredentials({
        refresh_token: refreshToken
      });

      const { credentials } = await this.oauth2Client.refreshAccessToken();
      
      return {
        accessToken: credentials.access_token,
        expiresAt: credentials.expiry_date,
        refreshToken: credentials.refresh_token || refreshToken // Keep old refresh token if new one not provided
      };
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }

  // Check if token is expired
  isTokenExpired(expiresAt) {
    if (!expiresAt) return true;
    return Date.now() >= expiresAt - (5 * 60 * 1000); // 5 minutes buffer
  }

  // Get valid access token (refresh if needed)
  async getValidAccessToken(account) {
    if (!this.isTokenExpired(account.expiresAt)) {
      return account.accessToken;
    }

    // Token is expired, refresh it
    try {
      const refreshedTokens = await this.refreshAccessToken(account.refreshToken);
      
      // Update the account with new tokens (this should be handled by the caller)
      return refreshedTokens.accessToken;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      throw new Error('Token refresh failed. Please re-authenticate.');
    }
  }

  // Create authenticated Gemini client
  createAuthenticatedClient(accessToken) {
    // This will be used to create a Gemini client with the access token
    // The exact implementation depends on how Google's Gemini SDK handles OAuth
    return {
      accessToken: accessToken,
      // Add other necessary configuration
    };
  }

  // Validate account credentials
  async validateAccount(account) {
    try {
      const accessToken = await this.getValidAccessToken(account);
      
      // Test the token by making a simple API call
      this.oauth2Client.setCredentials({ access_token: accessToken });
      const oauth2 = google.oauth2({ version: 'v2', auth: this.oauth2Client });
      const userInfo = await oauth2.userinfo.get();
      
      return {
        valid: true,
        email: userInfo.data.email,
        name: userInfo.data.name
      };
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  // Revoke account access
  async revokeAccount(accessToken) {
    try {
      await this.oauth2Client.revokeToken(accessToken);
      return true;
    } catch (error) {
      console.error('Error revoking token:', error);
      return false;
    }
  }

  // Get account quota information
  async getAccountQuota(accessToken) {
    // This would need to be implemented based on Google's quota API
    // For now, return default values
    return {
      requestsPerMinute: 60,
      requestsPerDay: 1000,
      requestsUsedToday: 0 // This would need to be tracked
    };
  }
}

export default GoogleAuthService;
