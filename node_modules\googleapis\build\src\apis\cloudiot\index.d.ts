/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { cloudiot_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof cloudiot_v1.Cloudiot;
};
export declare function cloudiot(version: 'v1'): cloudiot_v1.Cloudiot;
export declare function cloudiot(options: cloudiot_v1.Options): cloudiot_v1.Cloudiot;
declare const auth: AuthPlus;
export { auth };
export { cloudiot_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
