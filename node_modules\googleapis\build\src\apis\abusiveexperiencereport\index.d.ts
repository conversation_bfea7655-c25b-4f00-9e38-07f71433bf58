/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { abusiveexperiencereport_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof abusiveexperiencereport_v1.Abusiveexperiencereport;
};
export declare function abusiveexperiencereport(version: 'v1'): abusiveexperiencereport_v1.Abusiveexperiencereport;
export declare function abusiveexperiencereport(options: abusiveexperiencereport_v1.Options): abusiveexperiencereport_v1.Abusiveexperiencereport;
declare const auth: AuthPlus;
export { auth };
export { abusiveexperiencereport_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
