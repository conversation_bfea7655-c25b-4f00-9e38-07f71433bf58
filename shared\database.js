import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class Database {
  constructor() {
    this.db = null;
    this.dbPath = process.env.DB_PATH || join(__dirname, 'database.sqlite');
  }

  async init() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err);
          reject(err);
        } else {
          console.log('Connected to SQLite database');
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  async createTables() {
    const tables = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY,
        telegram_id TEXT UNIQUE NOT NULL,
        username TEXT,
        first_name TEXT,
        last_name TEXT,
        language_code TEXT DEFAULT 'ar',
        is_admin BOOLEAN DEFAULT 0,
        is_banned BOOLEAN DEFAULT 0,
        message_limit INTEGER DEFAULT 50,
        messages_used INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_active DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Messages table for chat history and usage tracking
      `CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        telegram_message_id TEXT,
        message_type TEXT, -- 'text', 'image', 'document', etc.
        content TEXT,
        response TEXT,
        tokens_used INTEGER DEFAULT 0,
        processing_time REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )`,
      
      // Bot settings table
      `CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        description TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Usage statistics table
      `CREATE TABLE IF NOT EXISTS usage_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT UNIQUE NOT NULL, -- YYYY-MM-DD format
        total_messages INTEGER DEFAULT 0,
        total_users INTEGER DEFAULT 0,
        total_tokens INTEGER DEFAULT 0,
        avg_response_time REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Custom messages table for personalized responses
      `CREATE TABLE IF NOT EXISTS custom_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        trigger_phrases TEXT NOT NULL, -- JSON array of trigger phrases
        responses TEXT NOT NULL, -- JSON object with language codes as keys
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    for (const table of tables) {
      await this.run(table);
    }

    // Insert default settings
    await this.insertDefaultSettings();
  }

  async insertDefaultSettings() {
    const defaultSettings = [
      { key: 'bot_status', value: 'active', description: 'Bot operational status' },
      { key: 'default_user_limit', value: '50', description: 'Default message limit for new users' },
      { key: 'maintenance_mode', value: 'false', description: 'Maintenance mode status' },
      { key: 'welcome_message_ar', value: 'مرحباً! أنا مساعد ذكي تم إنشاؤي من قبل امير (@sytus). يمكنني مساعدتك في تحليل النصوص والصور.', description: 'Welcome message in Arabic' },
      { key: 'welcome_message_en', value: 'Welcome! I am an AI assistant created by Amir (@sytus). I can help you analyze text and images.', description: 'Welcome message in English' }
    ];

    for (const setting of defaultSettings) {
      await this.run(
        'INSERT OR IGNORE INTO settings (key, value, description) VALUES (?, ?, ?)',
        [setting.key, setting.value, setting.description]
      );
    }
  }

  async run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('Database error:', err);
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  async get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          console.error('Database error:', err);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  async all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('Database error:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // User management methods
  async createUser(telegramUser) {
    const { id, username, first_name, last_name, language_code } = telegramUser;
    return await this.run(
      `INSERT OR REPLACE INTO users 
       (telegram_id, username, first_name, last_name, language_code, last_active) 
       VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
      [id.toString(), username, first_name, last_name, language_code || 'ar']
    );
  }

  async getUser(telegramId) {
    return await this.get(
      'SELECT * FROM users WHERE telegram_id = ?',
      [telegramId.toString()]
    );
  }

  async updateUserUsage(telegramId, tokensUsed = 0) {
    return await this.run(
      `UPDATE users
       SET messages_used = messages_used + 1, last_active = CURRENT_TIMESTAMP
       WHERE telegram_id = ?`,
      [telegramId.toString()]
    );
  }

  async updateUserLanguage(telegramId, languageCode) {
    return await this.run(
      `UPDATE users
       SET language_code = ?, last_active = CURRENT_TIMESTAMP
       WHERE telegram_id = ?`,
      [languageCode, telegramId.toString()]
    );
  }

  async getAllUsers() {
    return await this.all('SELECT * FROM users ORDER BY created_at DESC');
  }

  async getUserStats() {
    return await this.get(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN last_active > datetime('now', '-24 hours') THEN 1 END) as active_24h,
        COUNT(CASE WHEN last_active > datetime('now', '-7 days') THEN 1 END) as active_7d,
        SUM(messages_used) as total_messages
      FROM users
    `);
  }

  // Message logging
  async logMessage(userId, messageData) {
    const { telegram_message_id, message_type, content, response, tokens_used, processing_time } = messageData;
    return await this.run(
      `INSERT INTO messages 
       (user_id, telegram_message_id, message_type, content, response, tokens_used, processing_time) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [userId, telegram_message_id, message_type, content, response, tokens_used || 0, processing_time || 0]
    );
  }

  // Settings management
  async getSetting(key) {
    const result = await this.get('SELECT value FROM settings WHERE key = ?', [key]);
    return result ? result.value : null;
  }

  async setSetting(key, value) {
    return await this.run(
      'INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
      [key, value]
    );
  }

  async close() {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err);
          } else {
            console.log('Database connection closed');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

export default Database;
