<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Bot Manager</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-robot"></i> Bot Manager</h2>
            </div>
            <ul class="sidebar-menu">
                <li class="menu-item active" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </li>
                <li class="menu-item" data-page="accounts">
                    <i class="fab fa-google"></i> Accounts
                </li>
                <li class="menu-item" data-page="users">
                    <i class="fas fa-users"></i> Users
                </li>
                <li class="menu-item" data-page="analytics">
                    <i class="fas fa-chart-bar"></i> Analytics
                </li>
                <li class="menu-item" data-page="custom-messages">
                    <i class="fas fa-comments"></i> Custom Messages
                </li>
                <li class="menu-item" data-page="settings">
                    <i class="fas fa-cog"></i> Settings
                </li>
                <li class="menu-item" data-page="logs">
                    <i class="fas fa-file-alt"></i> Logs
                </li>
            </ul>
            <div class="sidebar-footer">
                <div class="bot-status">
                    <span class="status-indicator" id="botStatus"></span>
                    <span id="botStatusText">Checking...</span>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Page -->
            <div class="page active" id="dashboard">
                <div class="page-header">
                    <h1>Dashboard</h1>
                    <div class="header-actions">
                        <button class="btn btn-success" onclick="startBot()" id="startBotBtn">
                            <i class="fas fa-play"></i> Start Bot
                        </button>
                        <button class="btn btn-danger" onclick="stopBot()" id="stopBotBtn" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Bot
                        </button>
                        <button class="btn btn-secondary" onclick="openAccountsWindow()">
                            <i class="fab fa-google"></i> Manage Accounts
                        </button>
                        <button class="btn btn-primary" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Bot Status Section -->
                <div class="bot-status-section">
                    <div class="bot-status-card" id="botStatusCard">
                        <div class="bot-status-header">
                            <div class="bot-status-info">
                                <h3>Telegram Bot Status</h3>
                                <div class="bot-status-indicator">
                                    <span class="status-dot" id="botStatusDot"></span>
                                    <span id="botStatusText">Checking...</span>
                                </div>
                            </div>
                            <div class="bot-actions">
                                <button class="btn btn-sm btn-secondary" onclick="restartBot()" id="restartBotBtn">
                                    <i class="fas fa-redo"></i> Restart
                                </button>
                            </div>
                        </div>
                        <div class="bot-status-details">
                            <div class="status-detail">
                                <span>Active Account:</span>
                                <span id="botActiveAccount">None</span>
                            </div>
                            <div class="status-detail">
                                <span>Uptime:</span>
                                <span id="botUptime">0s</span>
                            </div>
                            <div class="status-detail">
                                <span>Port:</span>
                                <span id="botPort">3001</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalUsers">0</h3>
                            <p>Total Users</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="activeUsers24h">0</h3>
                            <p>Active (24h)</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalMessages">0</h3>
                            <p>Total Messages</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="activeUsers7d">0</h3>
                            <p>Active (7d)</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3>Recent Users</h3>
                        </div>
                        <div class="card-content">
                            <div class="table-container">
                                <table id="recentUsersTable">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Username</th>
                                            <th>Messages</th>
                                            <th>Last Active</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>Recent Messages</h3>
                        </div>
                        <div class="card-content">
                            <div class="messages-list" id="recentMessages"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Page -->
            <div class="page" id="users">
                <div class="page-header">
                    <h1>User Management</h1>
                    <div class="search-container">
                        <input type="text" id="userSearch" placeholder="Search users..." class="search-input">
                        <button class="btn btn-secondary" onclick="searchUsers()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-content">
                        <div class="table-container">
                            <table id="usersTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Username</th>
                                        <th>Messages Used</th>
                                        <th>Limit</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Page -->
            <div class="page" id="analytics">
                <div class="page-header">
                    <h1>Analytics</h1>
                    <select id="analyticsRange" onchange="loadAnalytics()">
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 90 days</option>
                    </select>
                </div>

                <div class="analytics-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3>Messages Over Time</h3>
                        </div>
                        <div class="card-content">
                            <canvas id="messagesChart"></canvas>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>User Activity</h3>
                        </div>
                        <div class="card-content">
                            <canvas id="usersChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Page -->
            <div class="page" id="settings">
                <div class="page-header">
                    <h1>Settings</h1>
                    <button class="btn btn-primary" onclick="saveSettings()">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                </div>

                <div class="settings-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3>Bot Configuration</h3>
                        </div>
                        <div class="card-content">
                            <div class="form-group">
                                <label for="defaultUserLimit">Default User Limit:</label>
                                <input type="number" id="defaultUserLimit" min="1" max="1000">
                            </div>
                            <div class="form-group">
                                <label for="welcomeMessageAr">Welcome Message (Arabic):</label>
                                <textarea id="welcomeMessageAr" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="welcomeMessageEn">Welcome Message (English):</label>
                                <textarea id="welcomeMessageEn" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="maintenanceMode">
                                    Maintenance Mode
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>System Status</h3>
                        </div>
                        <div class="card-content">
                            <div class="status-item">
                                <span>Bot Status:</span>
                                <span class="status-value" id="settingsBotStatus">Unknown</span>
                            </div>
                            <div class="status-item">
                                <span>Database:</span>
                                <span class="status-value connected">Connected</span>
                            </div>
                            <div class="status-item">
                                <span>Last Backup:</span>
                                <span class="status-value" id="lastBackup">Never</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Accounts Page -->
            <div class="page" id="accounts">
                <div class="page-header">
                    <h1><i class="fab fa-google"></i> Google Accounts</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="addNewAccount()">
                            <i class="fas fa-plus"></i> Add Account
                        </button>
                        <button class="btn btn-secondary" onclick="refreshAccountsData()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Account Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalAccountsCount">0</h3>
                            <p>Total Accounts</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalQuotaUsedCount">0</h3>
                            <p>Requests Used Today</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-battery-three-quarters"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalQuotaLimitCount">0</h3>
                            <p>Total Daily Limit</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="quotaPercentageCount">0%</h3>
                            <p>Quota Used</p>
                        </div>
                    </div>
                </div>

                <!-- Active Account Section -->
                <div class="card">
                    <div class="card-header">
                        <h3>Active Account</h3>
                    </div>
                    <div class="card-content">
                        <div class="active-account-card" id="activeAccountCardMain">
                            <div class="account-info">
                                <img id="activeAccountPictureMain" src="" alt="Profile" class="account-avatar" style="display: none;">
                                <div class="account-details">
                                    <h3 id="activeAccountNameMain">No active account</h3>
                                    <p id="activeAccountEmailMain">Please add and select an account</p>
                                    <div class="account-quota">
                                        <span id="activeAccountQuotaMain">0/0 requests used</span>
                                        <div class="quota-bar">
                                            <div class="quota-fill" id="activeAccountQuotaBarMain" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- All Accounts Section -->
                <div class="card">
                    <div class="card-header">
                        <h3>All Accounts</h3>
                    </div>
                    <div class="card-content">
                        <div class="accounts-grid" id="accountsGridMain">
                            <!-- Accounts will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Loading Indicator -->
                <div class="loading-overlay" id="loadingOverlayMain" style="display: none;">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Authenticating with Google...</p>
                    </div>
                </div>
            </div>

            <!-- Custom Messages Page -->
            <div class="page" id="custom-messages">
                <div class="page-header">
                    <h1>Custom Messages</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="showAddMessageForm()">
                            <i class="fas fa-plus"></i> Add Custom Message
                        </button>
                        <button class="btn btn-secondary" onclick="refreshCustomMessages()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Add Message Form -->
                <div class="card" id="addMessageCard" style="display: none;">
                    <div class="card-header">
                        <h3>Add New Custom Message</h3>
                    </div>
                    <div class="card-content">
                        <form id="customMessageForm">
                            <div class="form-group">
                                <label for="messageName">Message Name</label>
                                <input type="text" id="messageName" class="form-control" placeholder="Enter a name for this custom message" required>
                            </div>

                            <div class="form-group">
                                <label for="triggerPhrases">Trigger Phrases</label>
                                <textarea id="triggerPhrases" class="form-control" rows="3" placeholder="Enter trigger phrases (one per line)" required></textarea>
                                <small class="form-text">Enter phrases that will trigger this response (one per line)</small>
                            </div>

                            <div class="form-group">
                                <label>Responses by Language</label>
                                <div class="language-tabs">
                                    <button type="button" class="tab-btn active" onclick="switchLanguageTab('ar')">🇮🇶 Arabic</button>
                                    <button type="button" class="tab-btn" onclick="switchLanguageTab('en')">🇺🇸 English</button>
                                </div>
                                <div class="tab-content">
                                    <div class="tab-pane active" id="tab-ar">
                                        <textarea id="responseArabic" class="form-control" rows="4" placeholder="Enter Arabic response..."></textarea>
                                    </div>
                                    <div class="tab-pane" id="tab-en">
                                        <textarea id="responseEnglish" class="form-control" rows="4" placeholder="Enter English response..."></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Custom Message
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hideAddMessageForm()">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Messages List -->
                <div class="card">
                    <div class="card-header">
                        <h3>Existing Custom Messages</h3>
                    </div>
                    <div class="card-content">
                        <div id="customMessagesList">
                            <!-- Messages will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logs Page -->
            <div class="page" id="logs">
                <div class="page-header">
                    <h1>System Logs</h1>
                    <button class="btn btn-secondary" onclick="clearLogs()">
                        <i class="fas fa-trash"></i> Clear Logs
                    </button>
                </div>

                <div class="card">
                    <div class="card-content">
                        <div class="logs-container" id="logsContainer">
                            <div class="log-entry info">
                                <span class="log-time">[2024-01-01 12:00:00]</span>
                                <span class="log-level">INFO</span>
                                <span class="log-message">Application started successfully</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit User</h3>
                <button class="modal-close" onclick="closeModal('userModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="editUserLimit">Message Limit:</label>
                    <input type="number" id="editUserLimit" min="0" max="10000">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="editUserBanned">
                        Banned
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('userModal')">Cancel</button>
                <button class="btn btn-primary" onclick="saveUserChanges()">Save</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
