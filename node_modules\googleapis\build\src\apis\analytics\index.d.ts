/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { analytics_v3 } from './v3';
export declare const VERSIONS: {
    v3: typeof analytics_v3.Analytics;
};
export declare function analytics(version: 'v3'): analytics_v3.Analytics;
export declare function analytics(options: analytics_v3.Options): analytics_v3.Analytics;
declare const auth: AuthPlus;
export { auth };
export { analytics_v3 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
