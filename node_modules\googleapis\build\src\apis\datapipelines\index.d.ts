/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { datapipelines_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof datapipelines_v1.Datapipelines;
};
export declare function datapipelines(version: 'v1'): datapipelines_v1.Datapipelines;
export declare function datapipelines(options: datapipelines_v1.Options): datapipelines_v1.Datapipelines;
declare const auth: AuthPlus;
export { auth };
export { datapipelines_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
