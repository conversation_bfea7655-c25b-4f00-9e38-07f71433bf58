{"version": 3, "file": "NodeModuleCopyHelper.js", "sourceRoot": "", "sources": ["../../src/util/NodeModuleCopyHelper.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAA0C;AAC1C,uCAAoD;AACpD,6BAA4B;AAC5B,gDAA2D;AAE3D,uCAA2C;AAC3C,mDAAgD;AAEhD,2BAAiC;AAEjC,MAAM,aAAa,GAAG,IAAI,GAAG,CAC3B,CAAC,WAAW,EAAE,cAAc,CAAC,0BAA0B,EAAE,cAAc,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC,MAAM,CACpK,2BAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CACzB,CACF,CAAA;AACD,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAC;IACpC,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,iBAAiB;IACjB,QAAQ;IACR,WAAW;IACX,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,WAAW;IACX,cAAc;IACd,SAAS;IACT,UAAU;IACV,MAAM;CACP,CAAC,CAAA;AAEF,gBAAgB;AAChB,MAAa,oBAAqB,SAAQ,8BAAc;IACtD,YAAY,OAAoB,EAAE,QAAkB;QAClD,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,QAAQ,CAAC,CAAA;IAC7E,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,UAA0B,EAAE,sBAAqC;;QACxF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAE9B,MAAM,gBAAgB,GAAG,MAAM,IAAA,yBAAe,EAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAA;QAErI,MAAM,MAAM,GAA8B,EAAE,CAAA;QAC5C,MAAM,KAAK,GAAkB,EAAE,CAAA;QAC/B,MAAM,SAAS,GAAgB,IAAI,GAAG,EAAE,CAAA;QACxC,MAAM,YAAY,GAAwB,IAAI,GAAG,EAAE,CAAA;QACnD,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAA;QAC9B,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAA;QAClC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;QAChB,6FAA6F;QAC7F,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QACvC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAA;QAElB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,EAAG,CAAA;YAE5B,MAAM,UAAU,GAAG,MAAM,IAAA,kBAAO,EAAC,OAAO,CAAC,CAAA;YACzC,UAAU,CAAC,IAAI,EAAE,CAAA;YAEjB,MAAM,UAAU,GAAG,OAAO,KAAK,OAAO,CAAA;YACtC,MAAM,IAAI,GAAkB,EAAE,CAAA;YAC9B,mHAAmH;YACnH,MAAM,eAAe,GAAG,MAAM,sBAAe,CAAC,GAAG,CAC/C,UAAU,EACV,IAAI,CAAC,EAAE;gBACL,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;gBAEzC,MAAM,aAAa,GAAG,gBAAgB,IAAI,IAAI,IAAI,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;gBAE9E,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrD,OAAO,IAAI,CAAA;gBACb,CAAC;gBAED,kGAAkG;gBAClG,MAAM,WAAW,GAAG,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE,IAAA,oBAAS,EAAC,OAAO,CAAC,CAAC,CAAA;gBACzE,IAAI,CAAC,WAAW,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAC;oBACxF,KAAK,MAAM,GAAG,IAAI,sBAAsB,EAAE,CAAC;wBACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BACvB,OAAO,IAAI,CAAA;wBACb,CAAC;oBACH,CAAC;oBAED,uCAAuC;oBACvC,IAAI,UAAU,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,YAAY,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChJ,OAAO,IAAI,CAAA;oBACb,CAAC;oBAED,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC9B,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;4BACnI,OAAO,IAAI,CAAA;wBACb,CAAC;oBACH,CAAC;yBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC;wBACtF,OAAO,IAAI,CAAA;oBACb,CAAC;yBAAM,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC;wBACjG,OAAO,IAAI,CAAA;oBACb,CAAC;yBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;wBACpF,OAAO,IAAI,CAAA;oBACb,CAAC;gBACH,CAAC;gBAED,OAAO,IAAA,gBAAK,EAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACjC,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;wBAC9C,OAAO,IAAI,CAAA;oBACb,CAAC;oBAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACxB,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;oBAC9B,CAAC;oBACD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;oBAC/D,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;wBAC3B,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;4BACvB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BACf,OAAO,IAAI,CAAA;wBACb,CAAC;6BAAM,CAAC;4BACN,OAAO,QAAQ,CAAA;wBACjB,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,OAAO,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;4BAC9B,uDAAuD;4BACvD,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;gCAC3C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gCACf,OAAO,IAAI,CAAA;4BACb,CAAC;iCAAM,CAAC;gCACN,OAAO,QAAQ,CAAA;4BACjB,CAAC;wBACH,CAAC,CAAC,CAAA;oBACJ,CAAC;gBACH,CAAC,CAAC,CAAA;YACJ,CAAC,EACD,0BAAW,CACZ,CAAA;YAED,IAAI,OAAO,GAAG,IAAI,CAAA;YAClB,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;gBACpC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;oBAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBAClB,IAAI,MAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,0CAAE,cAAc,EAAE,EAAE,CAAC;wBAC/C,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;oBAC5C,CAAC;oBACD,OAAO,GAAG,KAAK,CAAA;gBACjB,CAAC;YACH,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACZ,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;YACxB,CAAC;YAED,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC;gBACzB,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAA;YACxC,CAAC;QACH,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,YAAY,EAAE,CAAC;YACzC,MAAM,YAAY,GAAG,IAAA,iBAAY,EAAC,IAAI,CAAC,CAAA;YACvC,IAAI,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,+CAA+C;gBAC/C,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,CAAA;YAC3B,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,EAAgB,EAAE,CAAC,EAAE,KAAK,SAAS,CAAC,CAAA;IAC9D,CAAC;CACF;AAnID,oDAmIC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { CONCURRENCY } from \"builder-util\"\nimport { lstat, readdir, lstatSync } from \"fs-extra\"\nimport * as path from \"path\"\nimport { excludedNames, FileMatcher } from \"../fileMatcher\"\nimport { Packager } from \"../packager\"\nimport { resolveFunction } from \"./resolve\"\nimport { FileCopyHelper } from \"./AppFileWalker\"\nimport { NodeModuleInfo } from \"./packageDependencies\"\nimport { realpathSync } from \"fs\"\n\nconst excludedFiles = new Set(\n  [\".DS_Store\", \"node_modules\" /* already in the queue */, \"CHANGELOG.md\", \"ChangeLog\", \"changelog.md\", \"Changelog.md\", \"Changelog\", \"binding.gyp\", \".npmignore\"].concat(\n    excludedNames.split(\",\")\n  )\n)\nconst topLevelExcludedFiles = new Set([\n  \"karma.conf.js\",\n  \".coveralls.yml\",\n  \"README.md\",\n  \"readme.markdown\",\n  \"README\",\n  \"readme.md\",\n  \"Readme.md\",\n  \"Readme\",\n  \"readme\",\n  \"test\",\n  \"tests\",\n  \"__tests__\",\n  \"powered-test\",\n  \"example\",\n  \"examples\",\n  \".bin\",\n])\n\n/** @internal */\nexport class NodeModuleCopyHelper extends FileCopyHelper {\n  constructor(matcher: FileMatcher, packager: Packager) {\n    super(matcher, matcher.isEmpty() ? null : matcher.createFilter(), packager)\n  }\n\n  async collectNodeModules(moduleInfo: NodeModuleInfo, nodeModuleExcludedExts: Array<string>): Promise<Array<string>> {\n    const filter = this.filter\n    const metadata = this.metadata\n\n    const onNodeModuleFile = await resolveFunction(this.packager.appInfo.type, this.packager.config.onNodeModuleFile, \"onNodeModuleFile\")\n\n    const result: Array<string | undefined> = []\n    const queue: Array<string> = []\n    const emptyDirs: Set<string> = new Set()\n    const symlinkFiles: Map<string, number> = new Map()\n    const tmpPath = moduleInfo.dir\n    const moduleName = moduleInfo.name\n    queue.length = 1\n    // The path should be corrected in Windows that when the moduleName is Scoped packages named.\n    const depPath = path.normalize(tmpPath)\n    queue[0] = depPath\n\n    while (queue.length > 0) {\n      const dirPath = queue.pop()!\n\n      const childNames = await readdir(dirPath)\n      childNames.sort()\n\n      const isTopLevel = dirPath === depPath\n      const dirs: Array<string> = []\n      // our handler is async, but we should add sorted files, so, we add file to result not in the mapper, but after map\n      const sortedFilePaths = await BluebirdPromise.map(\n        childNames,\n        name => {\n          const filePath = path.join(dirPath, name)\n\n          const forceIncluded = onNodeModuleFile != null && !!onNodeModuleFile(filePath)\n\n          if (excludedFiles.has(name) || name.startsWith(\"._\")) {\n            return null\n          }\n\n          // check if filematcher matches the files array as more important than the default excluded files.\n          const fileMatched = filter != null && filter(dirPath, lstatSync(dirPath))\n          if (!fileMatched || !forceIncluded || !!this.packager.config.disableDefaultIgnoredFiles) {\n            for (const ext of nodeModuleExcludedExts) {\n              if (name.endsWith(ext)) {\n                return null\n              }\n            }\n\n            // noinspection SpellCheckingInspection\n            if (isTopLevel && (topLevelExcludedFiles.has(name) || (moduleName === \"libui-node\" && (name === \"build\" || name === \"docs\" || name === \"src\")))) {\n              return null\n            }\n\n            if (dirPath.endsWith(\"build\")) {\n              if (name === \"gyp-mac-tool\" || name === \"Makefile\" || name.endsWith(\".mk\") || name.endsWith(\".gypi\") || name.endsWith(\".Makefile\")) {\n                return null\n              }\n            } else if (dirPath.endsWith(\"Release\") && (name === \".deps\" || name === \"obj.target\")) {\n              return null\n            } else if (name === \"src\" && (dirPath.endsWith(\"keytar\") || dirPath.endsWith(\"keytar-prebuild\"))) {\n              return null\n            } else if (dirPath.endsWith(\"lzma-native\") && (name === \"build\" || name === \"deps\")) {\n              return null\n            }\n          }\n\n          return lstat(filePath).then(stat => {\n            if (filter != null && !filter(filePath, stat)) {\n              return null\n            }\n\n            if (!stat.isDirectory()) {\n              metadata.set(filePath, stat)\n            }\n            const consumerResult = this.handleFile(filePath, dirPath, stat)\n            if (consumerResult == null) {\n              if (stat.isDirectory()) {\n                dirs.push(name)\n                return null\n              } else {\n                return filePath\n              }\n            } else {\n              return consumerResult.then(it => {\n                // asarUtil can return modified stat (symlink handling)\n                if ((it == null ? stat : it).isDirectory()) {\n                  dirs.push(name)\n                  return null\n                } else {\n                  return filePath\n                }\n              })\n            }\n          })\n        },\n        CONCURRENCY\n      )\n\n      let isEmpty = true\n      for (const child of sortedFilePaths) {\n        if (child != null) {\n          result.push(child)\n          if (this.metadata.get(child)?.isSymbolicLink()) {\n            symlinkFiles.set(child, result.length - 1)\n          }\n          isEmpty = false\n        }\n      }\n\n      if (isEmpty) {\n        emptyDirs.add(dirPath)\n      }\n\n      dirs.sort()\n      for (const child of dirs) {\n        queue.push(dirPath + path.sep + child)\n      }\n    }\n\n    for (const [file, index] of symlinkFiles) {\n      const resolvedPath = realpathSync(file)\n      if (emptyDirs.has(resolvedPath)) {\n        // delete symlink file if target is a empty dir\n        result[index] = undefined\n      }\n    }\n    return result.filter((it): it is string => it !== undefined)\n  }\n}\n"]}