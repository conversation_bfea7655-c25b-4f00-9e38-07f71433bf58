<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Messages</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .message-card {
            border-left: 4px solid #667eea;
            margin-bottom: 1rem;
        }
        .trigger-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 0.25rem 0.75rem;
            margin: 0.25rem;
            display: inline-block;
            font-size: 0.8rem;
        }
        .language-tab {
            border-radius: 10px 10px 0 0;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Custom Messages Management
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Add New Message Form -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <button class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#addMessageForm">
                                    <i class="fas fa-plus me-2"></i>Add New Custom Message
                                </button>
                            </div>
                        </div>

                        <div class="collapse" id="addMessageForm">
                            <div class="card mb-4">
                                <div class="card-body">
                                    <form id="customMessageForm">
                                        <div class="mb-3">
                                            <label for="messageName" class="form-label">Message Name</label>
                                            <input type="text" class="form-control" id="messageName" placeholder="Enter a name for this custom message" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="triggerPhrases" class="form-label">Trigger Phrases</label>
                                            <textarea class="form-control" id="triggerPhrases" rows="3" placeholder="Enter trigger phrases (one per line)&#10;Example:&#10;hello&#10;hi there&#10;good morning" required></textarea>
                                            <div class="form-text">Enter phrases that will trigger this response (one per line)</div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Responses by Language</label>
                                            <ul class="nav nav-tabs" id="languageTabs" role="tablist">
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link active language-tab" id="arabic-tab" data-bs-toggle="tab" data-bs-target="#arabic" type="button" role="tab">
                                                        🇮🇶 Arabic
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link language-tab" id="english-tab" data-bs-toggle="tab" data-bs-target="#english" type="button" role="tab">
                                                        🇺🇸 English
                                                    </button>
                                                </li>
                                            </ul>
                                            <div class="tab-content" id="languageTabContent">
                                                <div class="tab-pane fade show active" id="arabic" role="tabpanel">
                                                    <textarea class="form-control mt-2" id="responseArabic" rows="4" placeholder="Enter Arabic response..."></textarea>
                                                </div>
                                                <div class="tab-pane fade" id="english" role="tabpanel">
                                                    <textarea class="form-control mt-2" id="responseEnglish" rows="4" placeholder="Enter English response..."></textarea>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>Save Custom Message
                                            </button>
                                            <button type="button" class="btn btn-secondary" data-bs-toggle="collapse" data-bs-target="#addMessageForm">
                                                <i class="fas fa-times me-2"></i>Cancel
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Existing Messages List -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3">Existing Custom Messages</h5>
                                <div id="messagesList">
                                    <!-- Messages will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Message Modal -->
    <div class="modal fade" id="editMessageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Custom Message</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editMessageForm">
                        <input type="hidden" id="editMessageId">
                        <div class="mb-3">
                            <label for="editMessageName" class="form-label">Message Name</label>
                            <input type="text" class="form-control" id="editMessageName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editTriggerPhrases" class="form-label">Trigger Phrases</label>
                            <textarea class="form-control" id="editTriggerPhrases" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Responses by Language</label>
                            <ul class="nav nav-tabs" id="editLanguageTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active language-tab" id="edit-arabic-tab" data-bs-toggle="tab" data-bs-target="#edit-arabic" type="button" role="tab">
                                        🇮🇶 Arabic
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link language-tab" id="edit-english-tab" data-bs-toggle="tab" data-bs-target="#edit-english" type="button" role="tab">
                                        🇺🇸 English
                                    </button>
                                </li>
                            </ul>
                            <div class="tab-content" id="editLanguageTabContent">
                                <div class="tab-pane fade show active" id="edit-arabic" role="tabpanel">
                                    <textarea class="form-control mt-2" id="editResponseArabic" rows="4"></textarea>
                                </div>
                                <div class="tab-pane fade" id="edit-english" role="tabpanel">
                                    <textarea class="form-control mt-2" id="editResponseEnglish" rows="4"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editIsActive">
                                <label class="form-check-label" for="editIsActive">
                                    Active
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveEditMessage">
                        <i class="fas fa-save me-2"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/custom-messages.js"></script>
</body>
</html>
