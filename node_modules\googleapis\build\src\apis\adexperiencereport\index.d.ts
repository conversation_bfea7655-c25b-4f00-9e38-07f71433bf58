/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { adexperiencereport_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof adexperiencereport_v1.Adexperiencereport;
};
export declare function adexperiencereport(version: 'v1'): adexperiencereport_v1.Adexperiencereport;
export declare function adexperiencereport(options: adexperiencereport_v1.Options): adexperiencereport_v1.Adexperiencereport;
declare const auth: AuthPlus;
export { auth };
export { adexperiencereport_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
