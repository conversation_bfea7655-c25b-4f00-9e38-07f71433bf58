class CustomMessagesManager {
    constructor() {
        this.apiUrl = 'http://localhost:3001/api';
        this.init();
    }

    init() {
        this.loadMessages();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Add new message form
        document.getElementById('customMessageForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveNewMessage();
        });

        // Edit message form
        document.getElementById('saveEditMessage').addEventListener('click', () => {
            this.saveEditMessage();
        });
    }

    async loadMessages() {
        try {
            const response = await fetch(`${this.apiUrl}/custom-messages`);
            const messages = await response.json();
            this.renderMessages(messages);
        } catch (error) {
            console.error('Error loading messages:', error);
            this.showAlert('Error loading custom messages', 'danger');
        }
    }

    renderMessages(messages) {
        const container = document.getElementById('messagesList');
        
        if (messages.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-comments fa-3x mb-3"></i>
                    <p>No custom messages configured yet.</p>
                    <p>Click "Add New Custom Message" to get started.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = messages.map(message => {
            const triggers = JSON.parse(message.trigger_phrases);
            const responses = JSON.parse(message.responses);
            
            return `
                <div class="card message-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="card-title">
                                    ${message.name}
                                    ${message.is_active ? 
                                        '<span class="badge bg-success ms-2">Active</span>' : 
                                        '<span class="badge bg-secondary ms-2">Inactive</span>'
                                    }
                                </h6>
                                
                                <div class="mb-2">
                                    <small class="text-muted">Trigger phrases:</small><br>
                                    ${triggers.map(trigger => `<span class="trigger-badge">${trigger}</span>`).join('')}
                                </div>
                                
                                <div class="mb-2">
                                    <small class="text-muted">Responses:</small>
                                    <div class="mt-1">
                                        ${responses.ar ? `
                                            <div class="mb-1">
                                                <small class="fw-bold">🇮🇶 Arabic:</small>
                                                <div class="text-muted small">${this.truncateText(responses.ar, 100)}</div>
                                            </div>
                                        ` : ''}
                                        ${responses.en ? `
                                            <div class="mb-1">
                                                <small class="fw-bold">🇺🇸 English:</small>
                                                <div class="text-muted small">${this.truncateText(responses.en, 100)}</div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                                
                                <small class="text-muted">
                                    Created: ${new Date(message.created_at).toLocaleDateString()}
                                    ${message.updated_at !== message.created_at ? 
                                        `| Updated: ${new Date(message.updated_at).toLocaleDateString()}` : ''
                                    }
                                </small>
                            </div>
                            
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="customMessages.editMessage(${message.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="customMessages.deleteMessage(${message.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    async saveNewMessage() {
        try {
            const name = document.getElementById('messageName').value.trim();
            const triggerPhrasesText = document.getElementById('triggerPhrases').value.trim();
            const responseArabic = document.getElementById('responseArabic').value.trim();
            const responseEnglish = document.getElementById('responseEnglish').value.trim();

            if (!name || !triggerPhrasesText) {
                this.showAlert('Please fill in the required fields', 'warning');
                return;
            }

            if (!responseArabic && !responseEnglish) {
                this.showAlert('Please provide at least one response (Arabic or English)', 'warning');
                return;
            }

            const triggerPhrases = triggerPhrasesText.split('\n').map(phrase => phrase.trim()).filter(phrase => phrase);
            const responses = {};
            
            if (responseArabic) responses.ar = responseArabic;
            if (responseEnglish) responses.en = responseEnglish;

            const response = await fetch(`${this.apiUrl}/custom-messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name,
                    triggerPhrases,
                    responses
                })
            });

            if (response.ok) {
                this.showAlert('Custom message saved successfully!', 'success');
                this.clearForm();
                this.loadMessages();
                
                // Collapse the form
                const collapseElement = document.getElementById('addMessageForm');
                const bsCollapse = new bootstrap.Collapse(collapseElement, { toggle: false });
                bsCollapse.hide();
            } else {
                throw new Error('Failed to save message');
            }
        } catch (error) {
            console.error('Error saving message:', error);
            this.showAlert('Error saving custom message', 'danger');
        }
    }

    async editMessage(id) {
        try {
            const response = await fetch(`${this.apiUrl}/custom-messages`);
            const messages = await response.json();
            const message = messages.find(m => m.id === id);
            
            if (!message) {
                this.showAlert('Message not found', 'danger');
                return;
            }

            const triggers = JSON.parse(message.trigger_phrases);
            const responses = JSON.parse(message.responses);

            // Populate edit form
            document.getElementById('editMessageId').value = message.id;
            document.getElementById('editMessageName').value = message.name;
            document.getElementById('editTriggerPhrases').value = triggers.join('\n');
            document.getElementById('editResponseArabic').value = responses.ar || '';
            document.getElementById('editResponseEnglish').value = responses.en || '';
            document.getElementById('editIsActive').checked = message.is_active;

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editMessageModal'));
            modal.show();
        } catch (error) {
            console.error('Error loading message for edit:', error);
            this.showAlert('Error loading message', 'danger');
        }
    }

    async saveEditMessage() {
        try {
            const id = document.getElementById('editMessageId').value;
            const name = document.getElementById('editMessageName').value.trim();
            const triggerPhrasesText = document.getElementById('editTriggerPhrases').value.trim();
            const responseArabic = document.getElementById('editResponseArabic').value.trim();
            const responseEnglish = document.getElementById('editResponseEnglish').value.trim();
            const isActive = document.getElementById('editIsActive').checked;

            if (!name || !triggerPhrasesText) {
                this.showAlert('Please fill in the required fields', 'warning');
                return;
            }

            if (!responseArabic && !responseEnglish) {
                this.showAlert('Please provide at least one response (Arabic or English)', 'warning');
                return;
            }

            const triggerPhrases = triggerPhrasesText.split('\n').map(phrase => phrase.trim()).filter(phrase => phrase);
            const responses = {};
            
            if (responseArabic) responses.ar = responseArabic;
            if (responseEnglish) responses.en = responseEnglish;

            const response = await fetch(`${this.apiUrl}/custom-messages/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name,
                    triggerPhrases,
                    responses,
                    isActive
                })
            });

            if (response.ok) {
                this.showAlert('Custom message updated successfully!', 'success');
                this.loadMessages();
                
                // Hide modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('editMessageModal'));
                modal.hide();
            } else {
                throw new Error('Failed to update message');
            }
        } catch (error) {
            console.error('Error updating message:', error);
            this.showAlert('Error updating custom message', 'danger');
        }
    }

    async deleteMessage(id) {
        if (!confirm('Are you sure you want to delete this custom message?')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiUrl}/custom-messages/${id}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showAlert('Custom message deleted successfully!', 'success');
                this.loadMessages();
            } else {
                throw new Error('Failed to delete message');
            }
        } catch (error) {
            console.error('Error deleting message:', error);
            this.showAlert('Error deleting custom message', 'danger');
        }
    }

    clearForm() {
        document.getElementById('customMessageForm').reset();
    }

    showAlert(message, type) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at top of container
        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Initialize when page loads
const customMessages = new CustomMessagesManager();
