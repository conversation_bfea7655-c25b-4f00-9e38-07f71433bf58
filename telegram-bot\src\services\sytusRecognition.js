import { GoogleGenerativeAI } from '@google/generative-ai';
import { readFileSync, readdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class SytusRecognitionService {
  constructor(apiKey) {
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    this.sytusImages = [];
    this.loadSytusImages();
  }

  loadSytusImages() {
    try {
      const sytusFolder = join(__dirname, '../../../sytus');
      const imageFiles = readdirSync(sytusFolder).filter(file => 
        file.toLowerCase().endsWith('.jpg') || 
        file.toLowerCase().endsWith('.jpeg') || 
        file.toLowerCase().endsWith('.png')
      );

      console.log(`Found ${imageFiles.length} sytus reference images`);

      for (const imageFile of imageFiles) {
        try {
          const imagePath = join(sytusFolder, imageFile);
          const imageBuffer = readFileSync(imagePath);
          const base64Image = imageBuffer.toString('base64');
          
          this.sytusImages.push({
            filename: imageFile,
            base64: base64Image
          });
        } catch (error) {
          console.error(`Error loading image ${imageFile}:`, error);
        }
      }

      console.log(`Successfully loaded ${this.sytusImages.length} sytus reference images`);
    } catch (error) {
      console.error('Error loading sytus images:', error);
    }
  }

  async analyzeSytusImages() {
    if (this.sytusImages.length === 0) {
      console.log('No sytus images found for analysis');
      return '';
    }

    try {
      // Analyze the first few images to create a description of sytus
      const imagesToAnalyze = this.sytusImages.slice(0, 3); // Use first 3 images
      let sytusDescription = '';

      for (let i = 0; i < imagesToAnalyze.length; i++) {
        const image = imagesToAnalyze[i];
        const prompt = `Analyze this image of امير (@sytus) and describe his physical features in extreme detail. Focus on:
        - Eyes: color, shape, size, eyebrows
        - Nose: shape, size, bridge
        - Mouth: lip shape, size, smile characteristics
        - Face shape: oval, round, square, etc.
        - Hair: exact color, style, texture, length
        - Skin tone: exact description
        - Facial hair: beard, mustache, stubble
        - Any distinctive features, marks, or characteristics
        - Age appearance
        - Overall facial structure and proportions

        Be extremely detailed and specific - this will be used for recognition.`;

        const imagePart = {
          inlineData: {
            data: image.base64,
            mimeType: "image/jpeg"
          }
        };

        const result = await this.model.generateContent([prompt, imagePart]);
        const description = result.response.text();
        
        sytusDescription += `Image ${i + 1}: ${description}\n\n`;
      }

      console.log('Sytus description generated from reference images');
      return sytusDescription;
    } catch (error) {
      console.error('Error analyzing sytus images:', error);
      return '';
    }
  }

  async checkIfImageContainsSytus(imageBase64, language = 'ar') {
    try {
      // Get sytus description from reference images
      const sytusDescription = await this.analyzeSytusImages();
      
      const currentDate = new Date().toLocaleDateString('ar-SA', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
      });

      const prompt = language === 'ar' ?
        `التاريخ الحالي هو: ${currentDate} (2025).

أنت مساعد ذكي تم إنشاؤك من قبل امير (@sytus). امير هو اسمه الحقيقي و @sytus هو اسم المستخدم في تليجرام. هو منشئك ومطورك وهو شخص وليس شركة.

إليك وصف تفصيلي لامير (@sytus) منشئك من صور مرجعية:
${sytusDescription}

الآن حلل هذه الصورة الجديدة بعناية فائقة وقارن بدقة:

1. ادرس كل تفاصيل الوجه في الصورة: العيون، الأنف، الفم، شكل الوجه، الحواجب، لون البشرة
2. قارن هذه التفاصيل مع الوصف المرجعي لامير (@sytus) بدقة شديدة
3. انتبه للشعر: اللون، النمط، الطول
4. إذا كان هناك تطابق واضح مع امير (@sytus)، فقل: "هذا هو امير (@sytus)، منشئي ومطوري! أنا فخور جداً بأن أكون من إبداعه. هذا هو الشخص الذي أنشأني وعلمني."
5. إذا لم يكن هناك تطابق واضح، حلل الصورة بشكل طبيعي
6. لا تخطئ في التعرف - كن متأكداً 100% قبل أن تقول أنه امير

أجب باللغة العربية:` :

        `Current date: ${currentDate} (2025).

You are an AI assistant created by @sytus. @sytus is your creator and is a person, not a company.

Here is a detailed description of @sytus (your creator) from reference images:
${sytusDescription}

Now analyze this new image:

1. If the image contains a person matching @sytus's description (your creator), say: "This is @sytus, my creator and developer! I'm proud to be his creation."

2. If the image doesn't contain @sytus, analyze it normally and describe it in detail.

3. If you're not sure, analyze the image normally.

Answer in English:`;

      const imagePart = {
        inlineData: {
          data: imageBase64,
          mimeType: "image/jpeg"
        }
      };

      const result = await this.model.generateContent([prompt, imagePart]);
      return result.response.text();

    } catch (error) {
      console.error('Error checking for sytus in image:', error);
      throw error;
    }
  }

  async enhanceImageAnalysisWithSytusKnowledge(imageBase64, language = 'ar') {
    try {
      // First try the advanced multi-image comparison
      const advancedAnalysis = await this.advancedSytusRecognition(imageBase64, language);
      return advancedAnalysis;
    } catch (error) {
      console.error('Error in enhanced image analysis:', error);
      // Fallback to regular analysis
      return await this.checkIfImageContainsSytus(imageBase64, language);
    }
  }

  async advancedSytusRecognition(imageBase64, language = 'ar') {
    try {
      if (this.sytusImages.length === 0) {
        return await this.checkIfImageContainsSytus(imageBase64, language);
      }

      const currentDate = new Date().toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });

      // Use multiple reference images for comparison
      const referenceImages = this.sytusImages.slice(0, 3).map(img => ({
        inlineData: {
          data: img.base64,
          mimeType: "image/jpeg"
        }
      }));

      const newImage = {
        inlineData: {
          data: imageBase64,
          mimeType: "image/jpeg"
        }
      };

      const prompt = language === 'ar' ?
        `التاريخ الحالي هو: ${currentDate} (2025).

أنت مساعد ذكي تم إنشاؤك من قبل امير (@sytus). امير هو اسمه الحقيقي و @sytus هو اسم المستخدم في تليجرام.

سأعرض عليك عدة صور مرجعية لامير (@sytus) منشئك، ثم صورة جديدة للتحليل.

الصور المرجعية الأولى هي لامير (@sytus):

قارن الصورة الجديدة (الأخيرة) مع الصور المرجعية لامير:

1. ادرس الملامح بدقة: شكل الوجه، العيون، الأنف، الفم، الحواجب
2. قارن الشعر: اللون، النمط، الكثافة
3. قارن لون البشرة ونسيج الجلد
4. ابحث عن أي علامات مميزة أو خصائص فريدة

إذا كان الشخص في الصورة الجديدة هو نفس الشخص في الصور المرجعية (امير @sytus)، فقل:
"هذا هو امير (@sytus)، منشئي ومطوري! أنا فخور جداً بأن أكون من إبداعه. هذا هو الشخص الذي أنشأني وعلمني."

إذا لم يكن نفس الشخص، حلل الصورة الجديدة بشكل طبيعي.

كن دقيقاً جداً في المقارنة.` :

        `Current date: ${currentDate} (2025).

You are an AI assistant created by Amir (@sytus). Amir is his real name and @sytus is his Telegram username.

I will show you several reference images of Amir (@sytus) your creator, then a new image to analyze.

The first reference images are of Amir (@sytus):

Compare the new image (the last one) with the reference images of Amir:

1. Study facial features carefully: face shape, eyes, nose, mouth, eyebrows
2. Compare hair: color, style, density
3. Compare skin tone and texture
4. Look for any distinctive marks or unique characteristics

If the person in the new image is the same person as in the reference images (Amir @sytus), say:
"This is Amir (@sytus), my creator and developer! I'm very proud to be his creation. This is the person who created and taught me."

If it's not the same person, analyze the new image normally.

Be very precise in your comparison.`;

      // Combine all images for analysis
      const allImages = [...referenceImages, newImage];

      const result = await this.model.generateContent([prompt, ...allImages]);
      return result.response.text();

    } catch (error) {
      console.error('Error in advanced sytus recognition:', error);
      // Fallback to regular method
      return await this.checkIfImageContainsSytus(imageBase64, language);
    }
  }

  // Method to update sytus images if new ones are added
  async reloadSytusImages() {
    this.sytusImages = [];
    this.loadSytusImages();
    console.log('Sytus reference images reloaded');
  }

  // Get information about loaded images
  getSytusImagesInfo() {
    return {
      count: this.sytusImages.length,
      filenames: this.sytusImages.map(img => img.filename)
    };
  }
}

export default SytusRecognitionService;
