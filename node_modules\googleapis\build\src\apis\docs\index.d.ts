/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { docs_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof docs_v1.Docs;
};
export declare function docs(version: 'v1'): docs_v1.Docs;
export declare function docs(options: docs_v1.Options): docs_v1.Docs;
declare const auth: AuthPlus;
export { auth };
export { docs_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
