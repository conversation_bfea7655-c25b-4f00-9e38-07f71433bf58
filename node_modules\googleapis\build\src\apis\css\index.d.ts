/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { css_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof css_v1.Css;
};
export declare function css(version: 'v1'): css_v1.Css;
export declare function css(options: css_v1.Options): css_v1.Css;
declare const auth: AuthPlus;
export { auth };
export { css_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
