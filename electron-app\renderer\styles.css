/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.sidebar-menu {
    flex: 1;
    list-style: none;
    padding: 20px 0;
}

.menu-item {
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background-color: rgba(255,255,255,0.1);
    border-left-color: #fff;
}

.menu-item.active {
    background-color: rgba(255,255,255,0.2);
    border-left-color: #fff;
}

.menu-item i {
    margin-right: 10px;
    width: 20px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.bot-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ffc107;
    animation: pulse 2s infinite;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
    animation: none;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Main Content Styles */
.main-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Bot Status Section */
.bot-status-section {
    margin-bottom: 30px;
}

.bot-status-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    border-left: 4px solid #ddd;
    transition: all 0.3s ease;
}

.bot-status-card.bot-running {
    border-left-color: #4caf50;
    background: linear-gradient(135deg, #f1f8e9 0%, #ffffff 100%);
}

.bot-status-card.bot-stopped {
    border-left-color: #f44336;
    background: linear-gradient(135deg, #ffebee 0%, #ffffff 100%);
}

.bot-status-card.bot-error {
    border-left-color: #ff9800;
    background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
}

.bot-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.bot-status-info h3 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 1.2rem;
}

.bot-status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ddd;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background-color: #4caf50;
}

.status-dot.offline {
    background-color: #f44336;
    animation: none;
}

.status-dot.error {
    background-color: #ff9800;
}

.status-dot.unknown {
    background-color: #9e9e9e;
}

.bot-status-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.status-detail {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(0,0,0,0.03);
    border-radius: 6px;
    font-size: 14px;
}

.status-detail span:first-child {
    color: #666;
    font-weight: 500;
}

.status-detail span:last-child {
    color: #333;
    font-weight: 600;
}

/* Button Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.card-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.card-content {
    padding: 20px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-content p {
    color: #666;
    font-size: 14px;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

tr:hover {
    background-color: #f8f9fa;
}

/* Messages List */
.messages-list {
    max-height: 400px;
    overflow-y: auto;
}

.message-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.message-content {
    flex: 1;
}

.message-user {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.message-text {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

.message-time {
    font-size: 12px;
    color: #999;
}

/* Search Container */
.search-container {
    display: flex;
    gap: 10px;
}

.search-input {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    width: 300px;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Status Items */
.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.status-item:last-child {
    border-bottom: none;
}

.status-value {
    font-weight: 600;
}

.status-value.connected {
    color: #28a745;
}

.status-value.disconnected {
    color: #dc3545;
}

/* Logs Container */
.logs-container {
    background-color: #1e1e1e;
    color: #fff;
    padding: 20px;
    border-radius: 6px;
    max-height: 500px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.log-entry {
    margin-bottom: 8px;
    display: flex;
    gap: 10px;
}

.log-time {
    color: #888;
}

.log-level {
    font-weight: bold;
    min-width: 60px;
}

.log-level.info {
    color: #17a2b8;
}

.log-level.warning {
    color: #ffc107;
}

.log-level.error {
    color: #dc3545;
}

.log-level.success {
    color: #28a745;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* User Actions */
.user-actions {
    display: flex;
    gap: 5px;
}

/* Badge Styles */
.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.badge.active {
    background-color: #d4edda;
    color: #155724;
}

.badge.banned {
    background-color: #f8d7da;
    color: #721c24;
}

.badge.admin {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 60px;
    }

    .sidebar-header h2,
    .menu-item span {
        display: none;
    }

    .menu-item {
        text-align: center;
    }
}

/* Google Accounts Management Styles */
.accounts-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.accounts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.accounts-header h1 {
    color: #333;
    font-size: 2rem;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.active-account-section {
    margin-bottom: 30px;
}

.active-account-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.active-account-card.has-account {
    border-left: 4px solid #4285f4;
}

.account-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.account-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.modal-account-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.account-details h3 {
    margin: 0 0 5px 0;
    color: #333;
}

.account-details p {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
}

.account-quota {
    font-size: 12px;
    color: #666;
}

.quota-bar {
    width: 200px;
    height: 6px;
    background-color: #e0e0e0;
    border-radius: 3px;
    margin-top: 5px;
    overflow: hidden;
}

.quota-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.quota-fill.low {
    background-color: #4caf50;
}

.quota-fill.medium {
    background-color: #ff9800;
}

.quota-fill.high {
    background-color: #f44336;
}

.accounts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.account-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.account-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 25px rgba(0,0,0,0.15);
}

.account-card.active {
    border-left: 4px solid #4285f4;
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.account-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.account-status {
    margin-left: auto;
}

.account-status.connected {
    color: #4caf50;
}

.account-status.error {
    color: #f44336;
}

.account-stats {
    margin-bottom: 15px;
}

.stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.stat-label {
    color: #666;
}

.stat-value {
    color: #333;
    font-weight: 500;
}

.account-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.active-badge {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.no-accounts {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-accounts i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.no-accounts h3 {
    margin-bottom: 10px;
    color: #333;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.loading-spinner i {
    font-size: 2rem;
    color: #4285f4;
    margin-bottom: 15px;
}

.account-modal-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.modal-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.modal-actions .btn {
    justify-content: flex-start;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1001;
    min-width: 300px;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-left: 4px solid #4caf50;
}

.toast-error {
    border-left: 4px solid #f44336;
}

.toast-info {
    border-left: 4px solid #2196f3;
}

.toast i {
    font-size: 1.2rem;
}

.toast-success i {
    color: #4caf50;
}

.toast-error i {
    color: #f44336;
}

.toast-info i {
    color: #2196f3;
}

/* Custom Messages Styles */
.message-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.message-header h4 {
    margin: 0;
    color: #333;
}

.message-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-badge.inactive {
    background: #fafafa;
    color: #757575;
}

.triggers {
    margin-bottom: 12px;
}

.trigger-tag {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin: 2px 4px 2px 0;
}

.responses {
    margin-bottom: 12px;
}

.response-item {
    margin: 4px 0;
    padding: 8px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 14px;
}

.lang-flag {
    margin-right: 8px;
}

.message-meta {
    color: #999;
    font-size: 12px;
}

.language-tabs {
    display: flex;
    margin-bottom: 8px;
}

.tab-btn {
    background: #f5f5f5;
    border: 1px solid #ddd;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 4px 4px 0 0;
    margin-right: 2px;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.tab-content {
    border: 1px solid #ddd;
    border-radius: 0 4px 4px 4px;
}

.tab-pane {
    display: none;
    padding: 8px;
}

.tab-pane.active {
    display: block;
}

.form-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: #666;
}

.empty-state i {
    color: #ccc;
    margin-bottom: 16px;
}

.empty-state h3 {
    margin: 16px 0 8px 0;
    color: #333;
}

.form-text {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.modal-close:hover {
    background: #e0e0e0;
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-body .form-group {
    margin-bottom: 16px;
}

.modal-body .form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.modal-body .form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.modal-body label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #333;
}

.modal-body .form-actions {
    display: flex;
    gap: 8px;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;
}

.modal-body .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.modal-body .btn-primary {
    background: #667eea;
    color: white;
}

.modal-body .btn-primary:hover {
    background: #5a6fd8;
}

.modal-body .btn-secondary {
    background: #6c757d;
    color: white;
}

.modal-body .btn-secondary:hover {
    background: #5a6268;
}
