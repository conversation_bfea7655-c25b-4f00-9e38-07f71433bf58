import { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog } from 'electron';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import Database from '../../shared/database.js';
import AccountManager from '../../shared/accountManager.js';
import GoogleAuthService from '../../shared/googleAuth.js';
import { spawn } from 'child_process';

// Keep track of bot process
let botProcess = null;
import axios from 'axios';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class ElectronApp {
  constructor() {
    this.mainWindow = null;
    this.accountsWindow = null;
    this.db = new Database();
    this.accountManager = new AccountManager();
    this.googleAuth = new GoogleAuthService();
    this.botProcess = null;
    this.botApiUrl = `http://localhost:${process.env.BOT_PORT || 3001}`;
  }

  async init() {
    // Initialize database
    await this.db.init();
    
    // Set up app event handlers
    app.whenReady().then(() => {
      this.createWindow();
      this.setupMenu();
      this.setupIPC();
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Handle app quit - terminate bot process
    app.on('before-quit', async (event) => {
      if (botProcess) {
        console.log('Terminating bot process before app quit...');
        event.preventDefault(); // Prevent immediate quit

        try {
          // Kill bot process
          botProcess.kill('SIGTERM');

          // Wait for process to exit or force kill after 5 seconds
          await new Promise((resolve) => {
            const timeout = setTimeout(() => {
              if (botProcess) {
                console.log('Force killing bot process...');
                botProcess.kill('SIGKILL');
                botProcess = null;
              }
              resolve();
            }, 5000);

            if (botProcess) {
              botProcess.on('close', () => {
                clearTimeout(timeout);
                botProcess = null;
                console.log('Bot process terminated successfully');
                resolve();
              });
            } else {
              clearTimeout(timeout);
              resolve();
            }
          });

        } catch (error) {
          console.error('Error terminating bot process:', error);
        }

        // Now quit the app
        app.quit();
      }
    });

    // Handle app quit on macOS
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });
  }

  createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true
      },
      icon: join(__dirname, '../assets/icon.png'),
      title: 'Gemini Bot Manager',
      show: false
    });

    // Load the HTML file
    this.mainWindow.loadFile(join(__dirname, '../renderer/index.html'));

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
    });

    // Open DevTools in development
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.webContents.openDevTools();
    }

    // Handle window close - terminate bot if running
    this.mainWindow.on('close', async (event) => {
      if (botProcess) {
        console.log('Main window closing - terminating bot process...');
        event.preventDefault(); // Prevent immediate close

        try {
          // Kill bot process
          botProcess.kill('SIGTERM');

          // Wait for process to exit or force kill after 3 seconds
          await new Promise((resolve) => {
            const timeout = setTimeout(() => {
              if (botProcess) {
                console.log('Force killing bot process...');
                botProcess.kill('SIGKILL');
                botProcess = null;
              }
              resolve();
            }, 3000);

            if (botProcess) {
              botProcess.on('close', () => {
                clearTimeout(timeout);
                botProcess = null;
                console.log('Bot process terminated successfully');
                resolve();
              });
            } else {
              clearTimeout(timeout);
              resolve();
            }
          });

        } catch (error) {
          console.error('Error terminating bot process:', error);
        }

        // Now close the window
        this.mainWindow.destroy();
      }
    });
  }

  setupMenu() {
    const template = [
      {
        label: 'File',
        submenu: [
          {
            label: 'Export Data',
            click: async () => {
              await this.exportData();
            }
          },
          {
            label: 'Import Settings',
            click: async () => {
              await this.importSettings();
            }
          },
          { type: 'separator' },
          {
            label: 'Exit',
            accelerator: 'CmdOrCtrl+Q',
            click: () => {
              app.quit();
            }
          }
        ]
      },
      {
        label: 'Accounts',
        submenu: [
          {
            label: 'Manage Google Accounts',
            click: () => {
              this.openAccountsWindow();
            }
          },
          {
            label: 'Add Google Account',
            click: async () => {
              await this.addGoogleAccount();
            }
          },
          { type: 'separator' },
          {
            label: 'Reset Daily Quota',
            click: () => {
              this.accountManager.resetDailyQuota();
              dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'Quota Reset',
                message: 'Daily quota has been reset for all accounts.'
              });
            }
          }
        ]
      },
      {
        label: 'Bot',
        submenu: [
          {
            label: 'Start Bot',
            click: () => {
              this.mainWindow.webContents.send('bot-action', 'start');
            }
          },
          {
            label: 'Stop Bot',
            click: () => {
              this.mainWindow.webContents.send('bot-action', 'stop');
            }
          },
          {
            label: 'Restart Bot',
            click: () => {
              this.mainWindow.webContents.send('bot-action', 'restart');
            }
          }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Help',
        submenu: [
          {
            label: 'About',
            click: () => {
              dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'About Gemini Bot Manager',
                message: 'Gemini Bot Manager v1.0.0',
                detail: 'Desktop application for managing Gemini Telegram bot users and settings.'
              });
            }
          }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  setupIPC() {
    // Get dashboard data
    ipcMain.handle('get-dashboard-data', async () => {
      try {
        const userStats = await this.db.getUserStats();
        const recentUsers = await this.db.all(
          'SELECT * FROM users ORDER BY last_active DESC LIMIT 10'
        );
        const recentMessages = await this.db.all(`
          SELECT m.*, u.first_name, u.username 
          FROM messages m 
          JOIN users u ON m.user_id = u.id 
          ORDER BY m.created_at DESC 
          LIMIT 20
        `);

        // Try to get bot status
        let botStatus = 'unknown';
        try {
          const response = await axios.get(`${this.botApiUrl}/api/status`, { timeout: 5000 });
          botStatus = response.data.status;
        } catch (error) {
          botStatus = 'offline';
        }

        return {
          userStats,
          recentUsers,
          recentMessages,
          botStatus
        };
      } catch (error) {
        console.error('Error getting dashboard data:', error);
        throw error;
      }
    });

    // Get all users
    ipcMain.handle('get-users', async () => {
      try {
        return await this.db.getAllUsers();
      } catch (error) {
        console.error('Error getting users:', error);
        throw error;
      }
    });

    // Update user limit
    ipcMain.handle('update-user-limit', async (event, telegramId, newLimit) => {
      try {
        await this.db.run(
          'UPDATE users SET message_limit = ? WHERE telegram_id = ?',
          [newLimit, telegramId]
        );
        return { success: true };
      } catch (error) {
        console.error('Error updating user limit:', error);
        throw error;
      }
    });

    // Ban/unban user
    ipcMain.handle('toggle-user-ban', async (event, telegramId, isBanned) => {
      try {
        await this.db.run(
          'UPDATE users SET is_banned = ? WHERE telegram_id = ?',
          [isBanned ? 1 : 0, telegramId]
        );
        return { success: true };
      } catch (error) {
        console.error('Error toggling user ban:', error);
        throw error;
      }
    });

    // Reset user usage
    ipcMain.handle('reset-user-usage', async (event, telegramId) => {
      try {
        await this.db.run(
          'UPDATE users SET messages_used = 0 WHERE telegram_id = ?',
          [telegramId]
        );
        return { success: true };
      } catch (error) {
        console.error('Error resetting user usage:', error);
        throw error;
      }
    });

    // Get settings
    ipcMain.handle('get-settings', async () => {
      try {
        const settings = await this.db.all('SELECT * FROM settings');
        return settings.reduce((acc, setting) => {
          acc[setting.key] = setting.value;
          return acc;
        }, {});
      } catch (error) {
        console.error('Error getting settings:', error);
        throw error;
      }
    });

    // Update setting
    ipcMain.handle('update-setting', async (event, key, value) => {
      try {
        await this.db.setSetting(key, value);
        return { success: true };
      } catch (error) {
        console.error('Error updating setting:', error);
        throw error;
      }
    });

    // Get usage analytics
    ipcMain.handle('get-analytics', async (event, days = 7) => {
      try {
        const analytics = await this.db.all(`
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as message_count,
            COUNT(DISTINCT user_id) as unique_users,
            AVG(processing_time) as avg_processing_time
          FROM messages 
          WHERE created_at >= datetime('now', '-${days} days')
          GROUP BY DATE(created_at)
          ORDER BY date DESC
        `);
        return analytics;
      } catch (error) {
        console.error('Error getting analytics:', error);
        throw error;
      }
    });

    // Export data
    ipcMain.handle('export-data', async () => {
      return await this.exportData();
    });

    // Google Accounts Management
    ipcMain.handle('get-accounts-data', async () => {
      try {
        const stats = this.accountManager.getAccountStats();
        const accounts = this.accountManager.getAllAccounts();
        const activeAccount = this.accountManager.getActiveAccount();

        return { stats, accounts, activeAccount };
      } catch (error) {
        console.error('Error getting accounts data:', error);
        throw error;
      }
    });

    ipcMain.handle('add-google-account', async () => {
      return await this.addGoogleAccount();
    });

    ipcMain.handle('set-active-account', async (event, accountId) => {
      try {
        const success = this.accountManager.setActiveAccount(accountId);
        if (success) {
          // Notify the bot about the account change
          this.notifyBotAccountChange();
        }
        return success;
      } catch (error) {
        console.error('Error setting active account:', error);
        throw error;
      }
    });

    ipcMain.handle('remove-account', async (event, accountId) => {
      try {
        const success = this.accountManager.removeAccount(accountId);
        if (success) {
          this.notifyBotAccountChange();
        }
        return success;
      } catch (error) {
        console.error('Error removing account:', error);
        throw error;
      }
    });

    ipcMain.handle('refresh-active-account', async () => {
      try {
        const activeAccount = this.accountManager.getActiveAccount();
        if (!activeAccount) {
          throw new Error('No active account');
        }

        const validation = await this.googleAuth.validateAccount(activeAccount);
        if (!validation.valid) {
          throw new Error(validation.error);
        }

        return true;
      } catch (error) {
        console.error('Error refreshing active account:', error);
        throw error;
      }
    });

    // Bot Management
    ipcMain.handle('start-bot', async () => {
      return await this.startBot();
    });

    ipcMain.handle('stop-bot', async () => {
      return await this.stopBot();
    });

    ipcMain.handle('restart-bot', async () => {
      return await this.restartBot();
    });

    ipcMain.handle('open-accounts-window', async () => {
      this.openAccountsWindow();
      return true;
    });

    ipcMain.handle('get-bot-status', async () => {
      return await this.getBotStatus();
    });

    // Cleanup handler for manual bot termination
    ipcMain.handle('cleanup-bot', async () => {
      if (botProcess) {
        try {
          console.log('Manual cleanup - terminating bot process...');
          botProcess.kill('SIGTERM');

          // Wait for process to exit or force kill after 3 seconds
          await new Promise((resolve) => {
            const timeout = setTimeout(() => {
              if (botProcess) {
                console.log('Force killing bot process...');
                botProcess.kill('SIGKILL');
                botProcess = null;
              }
              resolve();
            }, 3000);

            if (botProcess) {
              botProcess.on('close', () => {
                clearTimeout(timeout);
                botProcess = null;
                console.log('Bot process terminated successfully');
                resolve();
              });
            } else {
              clearTimeout(timeout);
              resolve();
            }
          });

          return { success: true, message: 'Bot process terminated' };
        } catch (error) {
          console.error('Error during manual cleanup:', error);
          return { success: false, error: error.message };
        }
      } else {
        return { success: true, message: 'No bot process running' };
      }
    });

    // Custom Messages Management (Direct Database Access)
    ipcMain.handle('get-custom-messages', async () => {
      try {
        const messages = await this.db.all('SELECT * FROM custom_messages ORDER BY created_at DESC');
        return { success: true, data: messages };
      } catch (error) {
        console.error('Error getting custom messages:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('save-custom-message', async (event, messageData) => {
      try {
        const { name, triggerPhrases, responses } = messageData;

        const result = await this.db.run(
          'INSERT INTO custom_messages (name, trigger_phrases, responses) VALUES (?, ?, ?)',
          [name, JSON.stringify(triggerPhrases), JSON.stringify(responses)]
        );

        return { success: true, id: result.lastID };
      } catch (error) {
        console.error('Error saving custom message:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('update-custom-message', async (event, id, messageData) => {
      try {
        const { name, triggerPhrases, responses, isActive } = messageData;

        await this.db.run(
          'UPDATE custom_messages SET name = ?, trigger_phrases = ?, responses = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [name, JSON.stringify(triggerPhrases), JSON.stringify(responses), isActive, id]
        );

        return { success: true };
      } catch (error) {
        console.error('Error updating custom message:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('delete-custom-message', async (event, id) => {
      try {
        await this.db.run('DELETE FROM custom_messages WHERE id = ?', [id]);
        return { success: true };
      } catch (error) {
        console.error('Error deleting custom message:', error);
        return { success: false, error: error.message };
      }
    });
  }

  async exportData() {
    try {
      const { filePath } = await dialog.showSaveDialog(this.mainWindow, {
        title: 'Export Bot Data',
        defaultPath: `bot-data-${new Date().toISOString().split('T')[0]}.json`,
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if (filePath) {
        const users = await this.db.getAllUsers();
        const messages = await this.db.all('SELECT * FROM messages ORDER BY created_at DESC LIMIT 1000');
        const settings = await this.db.all('SELECT * FROM settings');

        const exportData = {
          exportDate: new Date().toISOString(),
          users: users,
          recentMessages: messages,
          settings: settings
        };

        const fs = await import('fs');
        await fs.promises.writeFile(filePath, JSON.stringify(exportData, null, 2));

        dialog.showMessageBox(this.mainWindow, {
          type: 'info',
          title: 'Export Complete',
          message: 'Data exported successfully!',
          detail: `Data saved to: ${filePath}`
        });

        return { success: true, filePath };
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      dialog.showErrorBox('Export Error', `Failed to export data: ${error.message}`);
      throw error;
    }
  }

  async importSettings() {
    try {
      const { filePaths } = await dialog.showOpenDialog(this.mainWindow, {
        title: 'Import Settings',
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        properties: ['openFile']
      });

      if (filePaths && filePaths.length > 0) {
        const fs = await import('fs');
        const data = JSON.parse(await fs.promises.readFile(filePaths[0], 'utf8'));

        if (data.settings) {
          for (const setting of data.settings) {
            await this.db.setSetting(setting.key, setting.value);
          }

          dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'Import Complete',
            message: 'Settings imported successfully!'
          });

          // Refresh the UI
          this.mainWindow.webContents.send('refresh-data');
        }
      }
    } catch (error) {
      console.error('Error importing settings:', error);
      dialog.showErrorBox('Import Error', `Failed to import settings: ${error.message}`);
    }
  }

  openAccountsWindow() {
    // Instead of opening a new window, navigate to accounts page in main window
    if (this.mainWindow) {
      this.mainWindow.webContents.send('navigate-to-accounts');
    }
  }

  async addGoogleAccount() {
    try {
      console.log('Starting Google OAuth flow...');
      const accountData = await this.googleAuth.startAuthFlow();

      console.log('OAuth successful, adding account...');
      const accountId = this.accountManager.addAccount(accountData);

      console.log('Account added successfully:', accountId);

      // Notify accounts window if open
      if (this.accountsWindow) {
        this.accountsWindow.webContents.send('account-added', accountData);
      }

      // Notify bot about new account
      this.notifyBotAccountChange();

      return { success: true, accountId };
    } catch (error) {
      console.error('Error adding Google account:', error);

      // Notify accounts window if open
      if (this.accountsWindow) {
        this.accountsWindow.webContents.send('account-error', error.message);
      }

      throw error;
    }
  }

  notifyBotAccountChange() {
    // Send notification to bot about account changes
    // This could be implemented as an API call to the bot
    try {
      const activeAccount = this.accountManager.getActiveAccount();
      console.log('Active account changed:', activeAccount ? activeAccount.email : 'None');

      // You could implement an API endpoint in the bot to receive this notification
      // axios.post(`${this.botApiUrl}/api/account-changed`, { activeAccount });
    } catch (error) {
      console.error('Error notifying bot about account change:', error);
    }
  }

  async startBot() {
    if (this.botProcess) {
      throw new Error('Bot is already running');
    }

    try {
      console.log('Starting Telegram bot...');

      const botPath = join(__dirname, '../../telegram-bot');

      this.botProcess = spawn('npm', ['start'], {
        cwd: botPath,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      // Store reference globally for cleanup
      botProcess = this.botProcess;

      this.botProcess.stdout.on('data', (data) => {
        console.log(`Bot stdout: ${data}`);
      });

      this.botProcess.stderr.on('data', (data) => {
        console.error(`Bot stderr: ${data}`);
      });

      this.botProcess.on('close', (code) => {
        console.log(`Bot process exited with code ${code}`);
        this.botProcess = null;
        if (this.mainWindow) {
          this.mainWindow.webContents.send('bot-status-changed', 'stopped');
        }
      });

      this.botProcess.on('error', (error) => {
        console.error('Bot process error:', error);
        this.botProcess = null;
        if (this.mainWindow) {
          this.mainWindow.webContents.send('bot-status-changed', 'error');
        }
      });

      // Wait for the bot to start and look for success message
      let botStarted = false;
      let attempts = 0;
      const maxAttempts = 10;

      while (!botStarted && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;

        // Check if bot is actually running
        const status = await this.getBotStatus();
        if (status.running) {
          botStarted = true;
          break;
        }
      }

      if (botStarted) {
        if (this.mainWindow) {
          this.mainWindow.webContents.send('bot-status-changed', 'running');
        }
        return { success: true, message: 'Bot started successfully' };
      } else {
        throw new Error('Bot failed to start properly within timeout');
      }

    } catch (error) {
      console.error('Error starting bot:', error);
      if (this.botProcess) {
        this.botProcess.kill();
        this.botProcess = null;
      }
      throw error;
    }
  }

  async stopBot() {
    if (!this.botProcess) {
      throw new Error('Bot is not running');
    }

    try {
      console.log('Stopping Telegram bot...');

      this.botProcess.kill('SIGTERM');

      // Wait for process to exit
      await new Promise((resolve) => {
        if (this.botProcess) {
          this.botProcess.on('close', resolve);
          // Force kill after 10 seconds
          setTimeout(() => {
            if (this.botProcess) {
              this.botProcess.kill('SIGKILL');
              resolve();
            }
          }, 10000);
        } else {
          resolve();
        }
      });

      this.botProcess = null;
      botProcess = null; // Clear global reference

      if (this.mainWindow) {
        this.mainWindow.webContents.send('bot-status-changed', 'stopped');
      }

      return { success: true, message: 'Bot stopped successfully' };

    } catch (error) {
      console.error('Error stopping bot:', error);
      throw error;
    }
  }

  async restartBot() {
    try {
      if (this.botProcess) {
        await this.stopBot();
        // Wait a moment before restarting
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      return await this.startBot();
    } catch (error) {
      console.error('Error restarting bot:', error);
      throw error;
    }
  }

  async getBotStatus() {
    try {
      const response = await axios.get(`${this.botApiUrl}/api/status`, {
        timeout: 5000
      });

      return {
        running: true,
        status: response.data.status,
        uptime: response.data.uptime,
        stats: response.data.stats
      };
    } catch (error) {
      return {
        running: false,
        status: 'offline',
        uptime: 0,
        error: error.message
      };
    }
  }
}

// Initialize the app
const electronApp = new ElectronApp();
electronApp.init().catch(console.error);
