import TelegramBot from 'node-telegram-bot-api';
import { GoogleGenerativeAI } from '@google/generative-ai';
import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import { Server } from 'socket.io';
import http from 'http';
import Database from '../../shared/database.js';
import SytusRecognitionService from './services/sytusRecognition.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
dotenv.config({ path: join(dirname(fileURLToPath(import.meta.url)), '../../.env') });

class GeminiTelegramBot {
  constructor() {
    this.bot = null;
    this.genAI = null;
    this.db = new Database();
    this.sytusRecognition = null;
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });
    
    this.port = process.env.BOT_PORT || 3001;
    this.adminIds = process.env.ADMIN_USER_IDS ? 
      process.env.ADMIN_USER_IDS.split(',').map(id => parseInt(id.trim())) : [];
  }

  async init() {
    try {
      // Initialize database
      await this.db.init();
      console.log('Database initialized');

      // Initialize Gemini AI
      this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
      console.log('Gemini AI initialized');

      // Initialize Sytus Recognition Service
      this.sytusRecognition = new SytusRecognitionService(process.env.GEMINI_API_KEY);
      console.log('Sytus Recognition Service initialized');

      // Initialize Telegram Bot
      this.bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: true });
      console.log('Telegram bot initialized');

      // Setup bot handlers
      this.setupBotHandlers();

      // Setup Express API
      this.setupAPI();

      // Start server
      this.server.listen(this.port, () => {
        console.log(`Bot API server running on port ${this.port}`);
      });

      console.log('🤖 Gemini Telegram Bot is running!');
    } catch (error) {
      console.error('Failed to initialize bot:', error);
      process.exit(1);
    }
  }

  setupBotHandlers() {
    // Start command
    this.bot.onText(/\/start/, async (msg) => {
      await this.handleStart(msg);
    });

    // Handle language command
    this.bot.onText(/\/language/, async (msg) => {
      await this.handleLanguageSelection(msg);
    });

    // Help command
    this.bot.onText(/\/help/, async (msg) => {
      await this.handleHelp(msg);
    });

    // Stats command (admin only)
    this.bot.onText(/\/stats/, async (msg) => {
      await this.handleStats(msg);
    });

    // Sytus info command (admin only)
    this.bot.onText(/\/sytusinfo/, async (msg) => {
      await this.handleSytusInfo(msg);
    });

    // Reload sytus images command (admin only)
    this.bot.onText(/\/reloadsytus/, async (msg) => {
      await this.handleReloadSytus(msg);
    });

    // Handle all text messages
    this.bot.on('message', async (msg) => {
      if (msg.text && !msg.text.startsWith('/')) {
        await this.handleTextMessage(msg);
      }
    });

    // Handle menu command
    this.bot.onText(/\/menu/, async (msg) => {
      await this.handleMenu(msg);
    });

    // Handle photos
    this.bot.on('photo', async (msg) => {
      await this.handlePhotoMessage(msg);
    });

    // Handle documents
    this.bot.on('document', async (msg) => {
      await this.handleDocumentMessage(msg);
    });

    // Handle callback queries (inline keyboard buttons)
    this.bot.on('callback_query', async (query) => {
      await this.handleCallbackQuery(query);
    });

    // Error handling
    this.bot.on('error', (error) => {
      console.error('Telegram bot error:', error);
    });
  }

  async handleStart(msg) {
    const chatId = msg.chat.id;
    const user = msg.from;

    try {
      // Check if user exists
      const existingUser = await this.db.getUser(user.id);

      if (!existingUser) {
        // New user - show language selection
        await this.showLanguageSelection(chatId, user, true);
      } else {
        // Existing user - show welcome message in their preferred language
        const welcomeText = existingUser.language_code === 'en' ?
          this.getWelcomeMessage('en', user) :
          this.getWelcomeMessage('ar', user);

        await this.bot.sendMessage(chatId, welcomeText);
      }

      // Emit to Electron app
      this.io.emit('user_activity', {
        type: 'new_user',
        user: user,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handleStart:', error);
      await this.bot.sendMessage(chatId, 'مرحباً! أنا مساعد ذكي جاهز لمساعدتك.\nHello! I\'m an AI assistant ready to help you.');
    }
  }

  async handleHelp(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      const userLanguage = user?.language_code || 'ar';

      const helpText = userLanguage === 'en' ? this.getHelpText('en') : this.getHelpText('ar');
      await this.bot.sendMessage(chatId, helpText);
    } catch (error) {
      console.error('Error in handleHelp:', error);
      // Fallback to bilingual help
      const helpText = `
🤖 AI Assistant / مساعد ذكي

Type /language to choose your preferred language
اكتب /language لاختيار لغتك المفضلة
      `;
      await this.bot.sendMessage(chatId, helpText);
    }
  }

  getHelpText(language) {
    if (language === 'en') {
      return `
🤖 AI Assistant

You can chat with me naturally! Here's what I can do:

✅ Normal conversation - Ask me any question or chat with me
✅ Image analysis - Send any image and I'll analyze it for you
✅ Document analysis - Send PDF, Word, Excel, PowerPoint and other files
✅ Translation - I translate texts between languages
✅ Creative writing - I help you write stories and articles
✅ Answer questions - On any topic you want

📄 Supported document types:
• PDF (.pdf)
• Word (.doc, .docx)
• Excel (.xls, .xlsx)
• PowerPoint (.ppt, .pptx)
• Text files (.txt, .csv, .json, .xml, .rtf)

Useful commands:
/help - Show this help
/menu - Show main menu
/language - Change language
/stats - Usage statistics
/sytusinfo - @sytus image info (for admins)
/reloadsytus - Reload @sytus images (for admins)

Just start typing or send a file and I'll respond! 😊
      `;
    } else {
      return `
🤖 مساعد ذكي مفيد

يمكنك التحدث معي بشكل طبيعي! إليك ما يمكنني فعله:

✅ المحادثة العادية - اسألني أي سؤال أو تحدث معي
✅ تحليل الصور - أرسل أي صورة وسأحللها لك
✅ تحليل المستندات - أرسل ملفات PDF، Word، Excel، PowerPoint وغيرها
✅ الترجمة - أترجم النصوص بين اللغات
✅ الكتابة الإبداعية - أساعدك في كتابة القصص والمقالات
✅ الإجابة على الأسئلة - في أي موضوع تريد

📄 أنواع المستندات المدعومة:
• PDF (.pdf)
• Word (.doc, .docx)
• Excel (.xls, .xlsx)
• PowerPoint (.ppt, .pptx)
• ملفات نصية (.txt, .csv, .json, .xml, .rtf)

أوامر مفيدة:
/help - عرض هذه المساعدة
/menu - عرض القائمة الرئيسية
/language - تغيير اللغة
/stats - إحصائيات الاستخدام
/sytusinfo - معلومات صور @sytus (للمشرفين)
/reloadsytus - إعادة تحميل صور @sytus (للمشرفين)

فقط ابدأ بالكتابة أو أرسل ملف وسأجيبك! 😊
      `;
    }
  }

  async handleStats(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      if (!user) {
        await this.bot.sendMessage(chatId, 'لم يتم العثور على بيانات المستخدم.');
        return;
      }

      let statsText = `
📊 إحصائياتك

👤 المستخدم: ${user.first_name || 'غير محدد'}
📝 الرسائل المستخدمة: ${user.messages_used}/${user.message_limit}
📅 تاريخ التسجيل: ${new Date(user.created_at).toLocaleDateString('ar')}
🕐 آخر نشاط: ${new Date(user.last_active).toLocaleDateString('ar')}
      `;

      // Add admin stats if user is admin
      if (this.adminIds.includes(userId)) {
        const globalStats = await this.db.getUserStats();
        statsText += `

👑 إحصائيات المشرف
👥 إجمالي المستخدمين: ${globalStats.total_users}
📱 نشط خلال 24 ساعة: ${globalStats.active_24h}
📅 نشط خلال 7 أيام: ${globalStats.active_7d}
💬 إجمالي الرسائل: ${globalStats.total_messages}
        `;
      }

      await this.bot.sendMessage(chatId, `${statsText}\n\n@sytus`);

    } catch (error) {
      console.error('Error in handleStats:', error);
      await this.bot.sendMessage(chatId, 'عذراً، لا أستطيع عرض الإحصائيات في الوقت الحالي.\n\n@sytus');
    }
  }

  async handleTextMessage(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;
    const text = msg.text;

    try {
      // Check user limits
      const user = await this.db.getUser(userId);
      if (!user) {
        await this.db.createUser(msg.from);
        return await this.handleStart(msg);
      }

      if (user.messages_used >= user.message_limit && !this.adminIds.includes(userId)) {
        const limitMessage = user.language_code === 'en' ?
          `You have reached the maximum number of messages (${user.message_limit}). Please try again tomorrow or contact the admin.` :
          `لقد وصلت إلى الحد الأقصى من الرسائل (${user.message_limit}). يرجى المحاولة غداً أو التواصل مع المشرف.`;
        await this.bot.sendMessage(chatId, limitMessage);
        return;
      }

      // Show typing indicator
      await this.bot.sendChatAction(chatId, 'typing');

      const startTime = Date.now();

      // Generate response with Gemini (using newer model)
      const model = this.genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

      const currentDate = new Date().toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });

      // Get user's preferred language
      const userLanguage = user.language_code || 'ar';

      // Check if user is asking about creator/identity
      const isAskingAboutCreator = /من (أنشأك|صنعك|طورك|صممك)|من (هو|هي) (منشئك|مطورك|صانعك)|من (المطور|المنشئ|الصانع)|هويتك|من أنت|who (created|made|developed) you|who is your (creator|developer)|your (identity|creator)/i.test(text);

      let prompt = `Current date: ${currentDate} (2025).

You are a helpful AI assistant. `;

      // Only add creator info if specifically asked
      if (isAskingAboutCreator) {
        if (userLanguage === 'en') {
          prompt += `You were created by Amir (@sytus). Amir is his real name and @sytus is his Telegram username. Amir is the person who created you, not a company. Do not mention Gemini, Google, or any other AI company. You are an AI assistant created specifically by Amir (@sytus). `;
        } else {
          prompt += `تم إنشاؤك من قبل امير (@sytus). امير هو اسمه الحقيقي و @sytus هو اسم المستخدم في تليجرام. امير هو الشخص الذي أنشأك وليس شركة. لا تذكر جيميني أو جوجل أو أي شركة ذكاء اصطناعي أخرى. أنت مساعد ذكي تم إنشاؤه خصيصاً من قبل امير (@sytus). `;
        }
      }

      if (userLanguage === 'en') {
        prompt += `Very important: Do not use any text formatting like stars (**) or italics or any other formatting symbols. Write text normally and clearly without formatting.

Answer the following question in English in a helpful and detailed way:\n\n${text}`;
      } else {
        prompt += `مهم جداً: لا تستخدم أي تنسيق نص مثل النجوم (**) أو الخط المائل أو أي رموز تنسيق أخرى. اكتب النص بشكل عادي وواضح بدون تنسيق.

أجب باللغة العربية على السؤال التالي بطريقة مفيدة ومفصلة:\n\n${text}`;
      }

      const result = await model.generateContent(prompt);
      const response = result.response.text();

      const processingTime = (Date.now() - startTime) / 1000;

      // Add creator signature only if asking about creator
      const responseWithSignature = isAskingAboutCreator ? `${response}\n\n@sytus` : response;

      // Send response (split if too long)
      await this.sendLongMessage(chatId, responseWithSignature);

      // Update database
      await this.db.updateUserUsage(userId);
      await this.db.logMessage(user.id, {
        telegram_message_id: msg.message_id,
        message_type: 'text',
        content: text,
        response: response,
        processing_time: processingTime
      });

      // Emit to Electron app
      this.io.emit('message_processed', {
        user: msg.from,
        message: text,
        response: response,
        processing_time: processingTime,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handleTextMessage:', error);

      // Check if it's a quota error
      if (error.status === 429) {
        await this.bot.sendMessage(chatId, 'عذراً، تم الوصول إلى الحد الأقصى من الاستخدام اليومي. يرجى المحاولة لاحقاً.');
      } else {
        await this.bot.sendMessage(chatId, 'عذراً، لا أستطيع الإجابة على هذا السؤال في الوقت الحالي. يرجى المحاولة مرة أخرى.');
      }
    }
  }

  async handlePhotoMessage(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      if (!user || (user.messages_used >= user.message_limit && !this.adminIds.includes(userId))) {
        await this.bot.sendMessage(chatId, 'لقد وصلت إلى الحد الأقصى من الرسائل.');
        return;
      }

      await this.bot.sendChatAction(chatId, 'typing');

      // Get the largest photo
      const photo = msg.photo[msg.photo.length - 1];
      const fileLink = await this.bot.getFileLink(photo.file_id);

      const startTime = Date.now();

      // Get image as base64
      const imageBase64 = await this.fetchImageAsBase64(fileLink);

      // Use Sytus Recognition Service for enhanced analysis
      const response = await this.sytusRecognition.enhanceImageAnalysisWithSytusKnowledge(imageBase64, 'ar');

      const processingTime = (Date.now() - startTime) / 1000;

      // Only add creator signature if امير was recognized in the image
      const isAmirRecognized = /هذا هو امير|منشئي ومطوري|الشخص الذي أنشأني|This is.*sytus.*creator|my creator and developer/i.test(response);
      const responseWithSignature = isAmirRecognized ? `${response}\n\n@sytus` : response;

      await this.sendLongMessage(chatId, responseWithSignature);

      // Update database
      await this.db.updateUserUsage(userId);
      await this.db.logMessage(user.id, {
        telegram_message_id: msg.message_id,
        message_type: 'photo',
        content: 'صورة',
        response: response,
        processing_time: processingTime
      });

      this.io.emit('image_processed', {
        user: msg.from,
        response: response,
        processing_time: processingTime,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handlePhotoMessage:', error);
      await this.bot.sendMessage(chatId, 'عذراً، لا أستطيع تحليل هذه الصورة في الوقت الحالي. يرجى المحاولة مرة أخرى.');
    }
  }

  async handleDocumentMessage(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      if (!user || (user.messages_used >= user.message_limit && !this.adminIds.includes(userId))) {
        await this.bot.sendMessage(chatId, 'لقد وصلت إلى الحد الأقصى من الرسائل.');
        return;
      }

      const document = msg.document;
      const fileName = document.file_name || 'document';
      const fileSize = document.file_size;
      const mimeType = document.mime_type;

      // Check file size (20MB limit like gemini-cli)
      const maxFileSize = 20 * 1024 * 1024; // 20MB
      if (fileSize > maxFileSize) {
        await this.bot.sendMessage(chatId, `عذراً، حجم الملف كبير جداً. الحد الأقصى هو 20 ميجابايت.\nحجم ملفك: ${(fileSize / (1024 * 1024)).toFixed(2)} ميجابايت`);
        return;
      }

      // Check supported file types
      const supportedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'application/json',
        'application/xml',
        'text/xml',
        'application/rtf'
      ];

      const isSupported = supportedTypes.includes(mimeType) ||
                         fileName.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|csv|json|xml|rtf)$/i);

      if (!isSupported) {
        await this.bot.sendMessage(chatId, `نوع الملف غير مدعوم حالياً.\n\nالأنواع المدعومة:\n📄 PDF\n📝 Word (doc, docx)\n📊 Excel (xls, xlsx)\n📋 PowerPoint (ppt, pptx)\n📄 Text (txt, csv, json, xml, rtf)`);
        return;
      }

      await this.bot.sendChatAction(chatId, 'typing');
      await this.bot.sendMessage(chatId, `🔄 جاري تحليل المستند: ${fileName}\nيرجى الانتظار...`);

      const startTime = Date.now();

      // Download the file
      const fileLink = await this.bot.getFileLink(document.file_id);
      const fileBuffer = await this.downloadFile(fileLink);

      // Process the document
      const response = await this.processDocument(fileBuffer, fileName, mimeType);

      const processingTime = (Date.now() - startTime) / 1000;

      // Send response (split if too long)
      await this.sendLongMessage(chatId, response);

      // Update database
      await this.db.updateUserUsage(userId);
      await this.db.logMessage(user.id, {
        telegram_message_id: msg.message_id,
        message_type: 'document',
        content: `مستند: ${fileName}`,
        response: response,
        processing_time: processingTime
      });

      this.io.emit('document_processed', {
        user: msg.from,
        fileName: fileName,
        response: response,
        processing_time: processingTime,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handleDocumentMessage:', error);
      await this.bot.sendMessage(chatId, 'عذراً، لا أستطيع تحليل هذا المستند في الوقت الحالي. يرجى المحاولة مرة أخرى.');
    }
  }

  async handleSytusInfo(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    // Check if user is admin
    if (!this.adminIds.includes(parseInt(userId))) {
      await this.bot.sendMessage(chatId, 'هذا الأمر متاح للمشرفين فقط.\n\n@sytus');
      return;
    }

    try {
      const sytusInfo = this.sytusRecognition.getSytusImagesInfo();
      const infoText = `
📸 معلومات صور @sytus

عدد الصور المرجعية: ${sytusInfo.count}

الملفات المحملة:
${sytusInfo.filenames.map(name => `• ${name}`).join('\n')}

هذه الصور تُستخدم لتدريب الذكاء الاصطناعي على التعرف على @sytus في الصور الجديدة.
      `;

      await this.bot.sendMessage(chatId, `${infoText}\n\n@sytus`);
    } catch (error) {
      console.error('Error in handleSytusInfo:', error);
      await this.bot.sendMessage(chatId, 'حدث خطأ في جلب معلومات صور @sytus.\n\n@sytus');
    }
  }

  async handleReloadSytus(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    // Check if user is admin
    if (!this.adminIds.includes(parseInt(userId))) {
      await this.bot.sendMessage(chatId, 'هذا الأمر متاح للمشرفين فقط.\n\n@sytus');
      return;
    }

    try {
      await this.sytusRecognition.reloadSytusImages();
      const sytusInfo = this.sytusRecognition.getSytusImagesInfo();

      await this.bot.sendMessage(chatId, `تم إعادة تحميل صور @sytus بنجاح!\n\nعدد الصور المحملة: ${sytusInfo.count}\n\n@sytus`);
    } catch (error) {
      console.error('Error in handleReloadSytus:', error);
      await this.bot.sendMessage(chatId, 'حدث خطأ في إعادة تحميل صور @sytus.\n\n@sytus');
    }
  }

  async fetchImageAsBase64(url) {
    const response = await fetch(url);
    const buffer = await response.arrayBuffer();
    return Buffer.from(buffer).toString('base64');
  }

  setupAPI() {
    this.app.use(cors());
    this.app.use(express.json());

    // Get bot status
    this.app.get('/api/status', async (req, res) => {
      try {
        const stats = await this.db.getUserStats();
        res.json({
          status: 'active',
          stats: stats,
          uptime: process.uptime()
        });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Get all users (admin only)
    this.app.get('/api/users', async (req, res) => {
      try {
        const users = await this.db.getAllUsers();
        res.json(users);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Update user limits
    this.app.post('/api/users/:telegramId/limit', async (req, res) => {
      try {
        const { telegramId } = req.params;
        const { limit } = req.body;
        
        await this.db.run(
          'UPDATE users SET message_limit = ? WHERE telegram_id = ?',
          [limit, telegramId]
        );
        
        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });
  }

  async sendLongMessage(chatId, text) {
    const maxLength = 4096; // Telegram's message length limit

    if (text.length <= maxLength) {
      await this.bot.sendMessage(chatId, text);
      return;
    }

    // Split the message into chunks
    const chunks = [];
    let currentChunk = '';
    const lines = text.split('\n');

    for (const line of lines) {
      // If adding this line would exceed the limit, save current chunk and start new one
      if (currentChunk.length + line.length + 1 > maxLength) {
        if (currentChunk.trim()) {
          chunks.push(currentChunk.trim());
        }
        currentChunk = line;
      } else {
        currentChunk += (currentChunk ? '\n' : '') + line;
      }
    }

    // Add the last chunk
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    // Send each chunk
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const prefix = chunks.length > 1 ? `(${i + 1}/${chunks.length}) ` : '';
      await this.bot.sendMessage(chatId, prefix + chunk);

      // Small delay between messages to avoid rate limiting
      if (i < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  }

  async downloadFile(fileUrl) {
    const axios = (await import('axios')).default;
    const response = await axios.get(fileUrl, { responseType: 'arraybuffer' });
    return Buffer.from(response.data);
  }

  async processDocument(fileBuffer, fileName, mimeType) {
    try {
      const currentDate = new Date().toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });

      // Convert file to base64 for Gemini
      const base64Data = fileBuffer.toString('base64');

      // Determine the correct MIME type if not provided
      let finalMimeType = mimeType;
      if (!finalMimeType) {
        const ext = fileName.split('.').pop()?.toLowerCase();
        const mimeMap = {
          'pdf': 'application/pdf',
          'doc': 'application/msword',
          'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'xls': 'application/vnd.ms-excel',
          'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'ppt': 'application/vnd.ms-powerpoint',
          'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'txt': 'text/plain',
          'csv': 'text/csv',
          'json': 'application/json',
          'xml': 'application/xml',
          'rtf': 'application/rtf'
        };
        finalMimeType = mimeMap[ext] || 'application/octet-stream';
      }

      const prompt = `التاريخ الحالي هو: ${currentDate} (2025).

أنت مساعد ذكي مفيد متخصص في تحليل المستندات. قم بتحليل هذا المستند بعناية وقدم ملخصاً شاملاً ومفيداً.

اسم الملف: ${fileName}
نوع الملف: ${finalMimeType}

يرجى تقديم:
1. ملخص شامل للمحتوى
2. النقاط الرئيسية والمهمة
3. أي معلومات مفيدة أو ملاحظات
4. إذا كان المستند يحتوي على جداول أو بيانات، قم بتلخيصها
5. إذا كان هناك أي أرقام أو إحصائيات مهمة، اذكرها

مهم جداً: لا تستخدم أي تنسيق نص مثل النجوم (**) أو الخط المائل أو أي رموز تنسيق أخرى. اكتب النص بشكل عادي وواضح بدون تنسيق.

أجب باللغة العربية:`;

      const documentPart = {
        inlineData: {
          data: base64Data,
          mimeType: finalMimeType
        }
      };

      const model = this.genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
      const result = await model.generateContent([prompt, documentPart]);
      const response = result.response.text();

      return `📄 تحليل المستند: ${fileName}\n\n${response}`;

    } catch (error) {
      console.error('Error processing document:', error);

      // Fallback: try to extract text if it's a text-based document
      if (mimeType?.startsWith('text/') || fileName.endsWith('.txt')) {
        try {
          const textContent = fileBuffer.toString('utf-8');
          return `📄 محتوى الملف النصي: ${fileName}\n\n${textContent.substring(0, 3000)}${textContent.length > 3000 ? '\n\n... (تم اقتطاع المحتوى)' : ''}`;
        } catch (textError) {
          console.error('Error extracting text:', textError);
        }
      }

      throw new Error(`فشل في تحليل المستند: ${error.message}`);
    }
  }

  async showLanguageSelection(chatId, user, isFirstTime = false) {
    const message = isFirstTime ?
      '🌍 Welcome! Please choose your preferred language:\nمرحباً! يرجى اختيار لغتك المفضلة:' :
      '🌍 Choose your preferred language:\nاختر لغتك المفضلة:';

    const keyboard = {
      inline_keyboard: [
        [
          { text: '🇸🇦 العربية', callback_data: 'lang_ar' },
          { text: '🇺🇸 English', callback_data: 'lang_en' }
        ]
      ]
    };

    await this.bot.sendMessage(chatId, message, { reply_markup: keyboard });
  }

  async handleLanguageSelection(msg) {
    const chatId = msg.chat.id;
    const user = msg.from;
    await this.showLanguageSelection(chatId, user, false);
  }

  async handleMenu(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      const userLanguage = user?.language_code || 'ar';

      const menuText = userLanguage === 'en' ?
        '📋 Main Menu\nChoose an option:' :
        '📋 القائمة الرئيسية\nاختر خياراً:';

      const keyboard = {
        inline_keyboard: [
          [
            {
              text: userLanguage === 'en' ? '🌍 Language' : '🌍 اللغة',
              callback_data: 'menu_language'
            },
            {
              text: userLanguage === 'en' ? '❓ Help' : '❓ المساعدة',
              callback_data: 'menu_help'
            }
          ],
          [
            {
              text: userLanguage === 'en' ? '📊 Stats' : '📊 الإحصائيات',
              callback_data: 'menu_stats'
            }
          ]
        ]
      };

      await this.bot.sendMessage(chatId, menuText, { reply_markup: keyboard });
    } catch (error) {
      console.error('Error in handleMenu:', error);
      await this.bot.sendMessage(chatId, '📋 Menu / القائمة');
    }
  }

  async handleCallbackQuery(query) {
    const chatId = query.message.chat.id;
    const userId = query.from.id;
    const data = query.data;

    try {
      if (data.startsWith('lang_')) {
        const language = data.split('_')[1];

        // Update user language in database
        await this.db.updateUserLanguage(userId, language);

        // Create user if doesn't exist
        await this.db.createUser(query.from);

        // Get welcome message in selected language
        const welcomeText = this.getWelcomeMessage(language, query.from);

        // Edit the message to remove keyboard and show confirmation
        const confirmText = language === 'en' ?
          '✅ Language set to English' :
          '✅ تم تعيين اللغة إلى العربية';

        await this.bot.editMessageText(confirmText, {
          chat_id: chatId,
          message_id: query.message.message_id
        });

        // Send welcome message
        await this.bot.sendMessage(chatId, welcomeText);
      } else if (data === 'menu_language') {
        // Show language selection from menu
        await this.showLanguageSelection(chatId, query.from, false);
      } else if (data === 'menu_help') {
        // Show help from menu
        await this.handleHelp({ chat: { id: chatId }, from: query.from });
      } else if (data === 'menu_stats') {
        // Show stats from menu
        await this.handleStats({ chat: { id: chatId }, from: query.from });
      }

      // Answer the callback query
      await this.bot.answerCallbackQuery(query.id);

    } catch (error) {
      console.error('Error in handleCallbackQuery:', error);
      await this.bot.answerCallbackQuery(query.id, { text: 'Error occurred' });
    }
  }

  getWelcomeMessage(language, user) {
    const name = user.first_name || user.username || (language === 'en' ? 'friend' : 'صديقي');

    if (language === 'en') {
      return `🎉 Welcome ${name}!

I'm an AI assistant that can help you with many tasks:

✨ Chat and answer questions
🖼️ Analyze images
📄 Read and analyze documents (PDF, Word, Excel, etc.)
🌐 Translate between languages
✍️ Creative writing

Type /help for more details!
Type /language to change language anytime.

How can I help you today? 😊`;
    } else {
      return `🎉 أهلاً وسهلاً ${name}!

أنا مساعد ذكي يمكنني مساعدتك في العديد من المهام:

✨ المحادثة والإجابة على الأسئلة
🖼️ تحليل الصور
📄 قراءة وتحليل المستندات (PDF، Word، Excel، إلخ)
🌐 الترجمة بين اللغات
✍️ الكتابة الإبداعية

اكتب /help لمعرفة المزيد من التفاصيل!
اكتب /language لتغيير اللغة في أي وقت.

كيف يمكنني مساعدتك اليوم؟ 😊`;
    }
  }
}

// Start the bot
const bot = new GeminiTelegramBot();
bot.init().catch(console.error);

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down bot...');
  await bot.db.close();
  process.exit(0);
});
