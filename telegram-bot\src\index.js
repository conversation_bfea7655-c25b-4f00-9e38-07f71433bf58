import TelegramBot from 'node-telegram-bot-api';
import { GoogleGenerativeAI } from '@google/generative-ai';
import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import { Server } from 'socket.io';
import http from 'http';
import Database from '../../shared/database.js';
import SytusRecognitionService from './services/sytusRecognition.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
dotenv.config({ path: join(dirname(fileURLToPath(import.meta.url)), '../../.env') });

class GeminiTelegramBot {
  constructor() {
    this.bot = null;
    this.genAI = null;
    this.db = new Database();
    this.sytusRecognition = null;
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });
    
    this.port = process.env.BOT_PORT || 3001;
    this.adminIds = process.env.ADMIN_USER_IDS ? 
      process.env.ADMIN_USER_IDS.split(',').map(id => parseInt(id.trim())) : [];
  }

  async init() {
    try {
      // Initialize database
      await this.db.init();
      console.log('Database initialized');

      // Initialize Gemini AI
      this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
      console.log('Gemini AI initialized');

      // Initialize Sytus Recognition Service
      this.sytusRecognition = new SytusRecognitionService(process.env.GEMINI_API_KEY);
      console.log('Sytus Recognition Service initialized');

      // Initialize Telegram Bot
      this.bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: true });
      console.log('Telegram bot initialized');

      // Setup bot handlers
      this.setupBotHandlers();

      // Setup Express API
      this.setupAPI();

      // Start server
      this.server.listen(this.port, () => {
        console.log(`Bot API server running on port ${this.port}`);
      });

      console.log('🤖 Gemini Telegram Bot is running!');
    } catch (error) {
      console.error('Failed to initialize bot:', error);
      process.exit(1);
    }
  }

  setupBotHandlers() {
    // Start command
    this.bot.onText(/\/start/, async (msg) => {
      await this.handleStart(msg);
    });

    // Help command
    this.bot.onText(/\/help/, async (msg) => {
      await this.handleHelp(msg);
    });

    // Stats command (admin only)
    this.bot.onText(/\/stats/, async (msg) => {
      await this.handleStats(msg);
    });

    // Sytus info command (admin only)
    this.bot.onText(/\/sytusinfo/, async (msg) => {
      await this.handleSytusInfo(msg);
    });

    // Reload sytus images command (admin only)
    this.bot.onText(/\/reloadsytus/, async (msg) => {
      await this.handleReloadSytus(msg);
    });

    // Handle all text messages
    this.bot.on('message', async (msg) => {
      if (msg.text && !msg.text.startsWith('/')) {
        await this.handleTextMessage(msg);
      }
    });

    // Handle photos
    this.bot.on('photo', async (msg) => {
      await this.handlePhotoMessage(msg);
    });

    // Handle documents
    this.bot.on('document', async (msg) => {
      await this.handleDocumentMessage(msg);
    });

    // Error handling
    this.bot.on('error', (error) => {
      console.error('Telegram bot error:', error);
    });
  }

  async handleStart(msg) {
    const chatId = msg.chat.id;
    const user = msg.from;

    try {
      // Create or update user in database
      await this.db.createUser(user);
      
      // Get welcome message
      const welcomeMessage = await this.db.getSetting('welcome_message_ar') ||
        'مرحباً! أنا مساعد ذكي تم إنشاؤي من قبل امير (@sytus). يمكنك التحدث معي بشكل طبيعي، أو إرسال صور لتحليلها. كيف يمكنني مساعدتك اليوم؟';

      await this.bot.sendMessage(chatId, `${welcomeMessage}\n\n@sytus`);

      // Emit to Electron app
      this.io.emit('user_activity', {
        type: 'new_user',
        user: user,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handleStart:', error);
      await this.bot.sendMessage(chatId, 'مرحباً! أنا مساعد ذكي تم إنشاؤي من قبل امير (@sytus). كيف يمكنني مساعدتك؟\n\n@sytus');
    }
  }

  async handleHelp(msg) {
    const chatId = msg.chat.id;

    const helpText = `
🤖 مساعد ذكي من امير (@sytus)

يمكنك التحدث معي بشكل طبيعي! إليك ما يمكنني فعله:

✅ المحادثة العادية - اسألني أي سؤال أو تحدث معي
✅ تحليل الصور - أرسل أي صورة وسأحللها لك
✅ الترجمة - أترجم النصوص بين اللغات
✅ الكتابة الإبداعية - أساعدك في كتابة القصص والمقالات
✅ الإجابة على الأسئلة - في أي موضوع تريد

أوامر مفيدة:
/help - عرض هذه المساعدة
/stats - إحصائيات الاستخدام
/sytusinfo - معلومات صور @sytus (للمشرفين)
/reloadsytus - إعادة تحميل صور @sytus (للمشرفين)

فقط ابدأ بالكتابة وسأجيبك! 😊
    `;

    await this.bot.sendMessage(chatId, `${helpText}\n\n@sytus`);
  }

  async handleStats(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      if (!user) {
        await this.bot.sendMessage(chatId, 'لم يتم العثور على بيانات المستخدم.');
        return;
      }

      let statsText = `
📊 إحصائياتك

👤 المستخدم: ${user.first_name || 'غير محدد'}
📝 الرسائل المستخدمة: ${user.messages_used}/${user.message_limit}
📅 تاريخ التسجيل: ${new Date(user.created_at).toLocaleDateString('ar')}
🕐 آخر نشاط: ${new Date(user.last_active).toLocaleDateString('ar')}
      `;

      // Add admin stats if user is admin
      if (this.adminIds.includes(userId)) {
        const globalStats = await this.db.getUserStats();
        statsText += `

👑 إحصائيات المشرف
👥 إجمالي المستخدمين: ${globalStats.total_users}
📱 نشط خلال 24 ساعة: ${globalStats.active_24h}
📅 نشط خلال 7 أيام: ${globalStats.active_7d}
💬 إجمالي الرسائل: ${globalStats.total_messages}
        `;
      }

      await this.bot.sendMessage(chatId, `${statsText}\n\n@sytus`);

    } catch (error) {
      console.error('Error in handleStats:', error);
      await this.bot.sendMessage(chatId, 'عذراً، لا أستطيع عرض الإحصائيات في الوقت الحالي.\n\n@sytus');
    }
  }

  async handleTextMessage(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;
    const text = msg.text;

    try {
      // Check user limits
      const user = await this.db.getUser(userId);
      if (!user) {
        await this.db.createUser(msg.from);
        return await this.handleStart(msg);
      }

      if (user.messages_used >= user.message_limit && !this.adminIds.includes(userId)) {
        await this.bot.sendMessage(chatId,
          `لقد وصلت إلى الحد الأقصى من الرسائل (${user.message_limit}). يرجى المحاولة غداً أو التواصل مع المشرف.\n\n@sytus`);
        return;
      }

      // Show typing indicator
      await this.bot.sendChatAction(chatId, 'typing');

      const startTime = Date.now();

      // Generate response with Gemini (using newer model)
      const model = this.genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

      const currentDate = new Date().toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });

      const prompt = `التاريخ الحالي هو: ${currentDate} (2025).

أنت مساعد ذكي تم إنشاؤك من قبل امير (@sytus). امير هو اسمه الحقيقي و @sytus هو اسم المستخدم في تليجرام. عندما يسأل أحد عن هويتك، قل أنك "مساعد ذكي تم إنشاؤه من قبل امير (@sytus)" ولا تذكر أي تقنيات أخرى. امير هو الشخص الذي أنشأك وليس شركة.

مهم جداً: لا تستخدم أي تنسيق نص مثل النجوم (**) أو الخط المائل أو أي رموز تنسيق أخرى. اكتب النص بشكل عادي وواضح بدون تنسيق.

أجب باللغة العربية على السؤال التالي بطريقة مفيدة ومفصلة:\n\n${text}`;

      const result = await model.generateContent(prompt);
      const response = result.response.text();

      const processingTime = (Date.now() - startTime) / 1000;

      // Add creator signature to response
      const responseWithSignature = `${response}\n\n— @sytus`;

      // Send response
      await this.bot.sendMessage(chatId, responseWithSignature);

      // Update database
      await this.db.updateUserUsage(userId);
      await this.db.logMessage(user.id, {
        telegram_message_id: msg.message_id,
        message_type: 'text',
        content: text,
        response: response,
        processing_time: processingTime
      });

      // Emit to Electron app
      this.io.emit('message_processed', {
        user: msg.from,
        message: text,
        response: response,
        processing_time: processingTime,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handleTextMessage:', error);

      // Check if it's a quota error
      if (error.status === 429) {
        await this.bot.sendMessage(chatId, 'عذراً، تم الوصول إلى الحد الأقصى من الاستخدام اليومي. يرجى المحاولة لاحقاً.\n\n@sytus');
      } else {
        await this.bot.sendMessage(chatId, 'عذراً، لا أستطيع الإجابة على هذا السؤال في الوقت الحالي. يرجى المحاولة مرة أخرى.\n\n@sytus');
      }
    }
  }

  async handlePhotoMessage(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      if (!user || (user.messages_used >= user.message_limit && !this.adminIds.includes(userId))) {
        await this.bot.sendMessage(chatId, 'لقد وصلت إلى الحد الأقصى من الرسائل.\n\n@sytus');
        return;
      }

      await this.bot.sendChatAction(chatId, 'typing');

      // Get the largest photo
      const photo = msg.photo[msg.photo.length - 1];
      const fileLink = await this.bot.getFileLink(photo.file_id);

      const startTime = Date.now();

      // Get image as base64
      const imageBase64 = await this.fetchImageAsBase64(fileLink);

      // Use Sytus Recognition Service for enhanced analysis
      const response = await this.sytusRecognition.enhanceImageAnalysisWithSytusKnowledge(imageBase64, 'ar');

      const processingTime = (Date.now() - startTime) / 1000;

      // Add creator signature to response
      const responseWithSignature = `${response}\n\n— @sytus`;

      await this.bot.sendMessage(chatId, responseWithSignature);

      // Update database
      await this.db.updateUserUsage(userId);
      await this.db.logMessage(user.id, {
        telegram_message_id: msg.message_id,
        message_type: 'photo',
        content: 'صورة',
        response: response,
        processing_time: processingTime
      });

      this.io.emit('image_processed', {
        user: msg.from,
        response: response,
        processing_time: processingTime,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handlePhotoMessage:', error);
      await this.bot.sendMessage(chatId, 'عذراً، لا أستطيع تحليل هذه الصورة في الوقت الحالي. يرجى المحاولة مرة أخرى.\n\n@sytus');
    }
  }

  async handleDocumentMessage(msg) {
    const chatId = msg.chat.id;
    await this.bot.sendMessage(chatId, 'ميزة تحليل المستندات ستكون متاحة قريباً!\n\n@sytus');
  }

  async handleSytusInfo(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    // Check if user is admin
    if (!this.adminIds.includes(parseInt(userId))) {
      await this.bot.sendMessage(chatId, 'هذا الأمر متاح للمشرفين فقط.\n\n@sytus');
      return;
    }

    try {
      const sytusInfo = this.sytusRecognition.getSytusImagesInfo();
      const infoText = `
📸 معلومات صور @sytus

عدد الصور المرجعية: ${sytusInfo.count}

الملفات المحملة:
${sytusInfo.filenames.map(name => `• ${name}`).join('\n')}

هذه الصور تُستخدم لتدريب الذكاء الاصطناعي على التعرف على @sytus في الصور الجديدة.
      `;

      await this.bot.sendMessage(chatId, `${infoText}\n\n@sytus`);
    } catch (error) {
      console.error('Error in handleSytusInfo:', error);
      await this.bot.sendMessage(chatId, 'حدث خطأ في جلب معلومات صور @sytus.\n\n@sytus');
    }
  }

  async handleReloadSytus(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    // Check if user is admin
    if (!this.adminIds.includes(parseInt(userId))) {
      await this.bot.sendMessage(chatId, 'هذا الأمر متاح للمشرفين فقط.\n\n@sytus');
      return;
    }

    try {
      await this.sytusRecognition.reloadSytusImages();
      const sytusInfo = this.sytusRecognition.getSytusImagesInfo();

      await this.bot.sendMessage(chatId, `تم إعادة تحميل صور @sytus بنجاح!\n\nعدد الصور المحملة: ${sytusInfo.count}\n\n@sytus`);
    } catch (error) {
      console.error('Error in handleReloadSytus:', error);
      await this.bot.sendMessage(chatId, 'حدث خطأ في إعادة تحميل صور @sytus.\n\n@sytus');
    }
  }

  async fetchImageAsBase64(url) {
    const response = await fetch(url);
    const buffer = await response.arrayBuffer();
    return Buffer.from(buffer).toString('base64');
  }

  setupAPI() {
    this.app.use(cors());
    this.app.use(express.json());

    // Get bot status
    this.app.get('/api/status', async (req, res) => {
      try {
        const stats = await this.db.getUserStats();
        res.json({
          status: 'active',
          stats: stats,
          uptime: process.uptime()
        });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Get all users (admin only)
    this.app.get('/api/users', async (req, res) => {
      try {
        const users = await this.db.getAllUsers();
        res.json(users);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Update user limits
    this.app.post('/api/users/:telegramId/limit', async (req, res) => {
      try {
        const { telegramId } = req.params;
        const { limit } = req.body;
        
        await this.db.run(
          'UPDATE users SET message_limit = ? WHERE telegram_id = ?',
          [limit, telegramId]
        );
        
        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });
  }
}

// Start the bot
const bot = new GeminiTelegramBot();
bot.init().catch(console.error);

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down bot...');
  await bot.db.close();
  process.exit(0);
});
