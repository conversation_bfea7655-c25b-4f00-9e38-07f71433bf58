import TelegramBot from 'node-telegram-bot-api';
import { GoogleGenerativeAI } from '@google/generative-ai';
import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import { Server } from 'socket.io';
import http from 'http';
import Database from '../../shared/database.js';
import SytusRecognitionService from './services/sytusRecognition.js';
import LanguageService from './services/languageService.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
dotenv.config({ path: join(dirname(fileURLToPath(import.meta.url)), '../../.env') });

class GeminiTelegramBot {
  constructor() {
    this.bot = null;
    this.genAI = null;
    this.db = new Database();
    this.sytusRecognition = null;
    this.languageService = new LanguageService();
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });
    
    this.port = process.env.BOT_PORT || 3001;
    this.adminIds = process.env.ADMIN_USER_IDS ? 
      process.env.ADMIN_USER_IDS.split(',').map(id => parseInt(id.trim())) : [];
  }

  async init() {
    try {
      // Initialize database
      await this.db.init();
      console.log('Database initialized');

      // Initialize Gemini AI
      this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
      console.log('Gemini AI initialized');

      // Initialize Sytus Recognition Service
      this.sytusRecognition = new SytusRecognitionService(process.env.GEMINI_API_KEY);
      console.log('Sytus Recognition Service initialized');

      // Initialize Telegram Bot
      this.bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: true });
      console.log('Telegram bot initialized');

      // Setup bot handlers
      this.setupBotHandlers();

      // Setup Express API
      this.setupAPI();

      // Start server
      this.server.listen(this.port, () => {
        console.log(`Bot API server running on port ${this.port}`);
      });

      console.log('🤖 Gemini Telegram Bot is running!');
    } catch (error) {
      console.error('Failed to initialize bot:', error);
      process.exit(1);
    }
  }

  setupBotHandlers() {
    // Start command
    this.bot.onText(/\/start/, async (msg) => {
      await this.handleStart(msg);
    });

    // Help command
    this.bot.onText(/\/help/, async (msg) => {
      await this.handleHelp(msg);
    });

    // Menu command
    this.bot.onText(/\/menu/, async (msg) => {
      await this.handleMenu(msg);
    });

    // Language command
    this.bot.onText(/\/language/, async (msg) => {
      await this.handleLanguage(msg);
    });

    // Stats command
    this.bot.onText(/\/stats/, async (msg) => {
      await this.handleStats(msg);
    });

    // Stats command (admin only)
    this.bot.onText(/\/stats/, async (msg) => {
      await this.handleStats(msg);
    });

    // Sytus info command (admin only)
    this.bot.onText(/\/sytusinfo/, async (msg) => {
      await this.handleSytusInfo(msg);
    });

    // Reload sytus images command (admin only)
    this.bot.onText(/\/reloadsytus/, async (msg) => {
      await this.handleReloadSytus(msg);
    });

    // Handle all text messages
    this.bot.on('message', async (msg) => {
      if (msg.text && !msg.text.startsWith('/')) {
        await this.handleTextMessage(msg);
      }
    });

    // Handle photos
    this.bot.on('photo', async (msg) => {
      await this.handlePhotoMessage(msg);
    });

    // Handle documents
    this.bot.on('document', async (msg) => {
      await this.handleDocumentMessage(msg);
    });

    // Handle callback queries (inline keyboard buttons)
    this.bot.on('callback_query', async (query) => {
      await this.handleCallbackQuery(query);
    });

    // Error handling
    this.bot.on('error', (error) => {
      console.error('Telegram bot error:', error);
    });
  }

  async handleStart(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;
    const user = msg.from;

    try {
      // Check if user exists
      const existingUser = await this.db.getUser(userId);

      if (!existingUser) {
        // New user - create and show language selection
        await this.db.createUser(user);

        const welcomeText = this.languageService.getText('welcome_new_user');
        const keyboard = this.languageService.createLanguageKeyboard();

        await this.bot.sendMessage(chatId, welcomeText, {
          reply_markup: keyboard
        });
      } else {
        // Existing user - welcome back in their language
        const userLang = existingUser.language_code || 'ar';
        const welcomeText = this.languageService.getText('welcome_back', userLang);
        const replyKeyboard = this.languageService.createReplyKeyboard(userLang);

        await this.bot.sendMessage(chatId, welcomeText, {
          reply_markup: replyKeyboard
        });
      }

      // Emit to Electron app
      this.io.emit('user_activity', {
        type: existingUser ? 'returning_user' : 'new_user',
        user: user,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handleStart:', error);
      await this.bot.sendMessage(chatId, 'مرحباً! أنا مساعد ذكي. كيف يمكنني مساعدتك؟');
    }
  }

  async handleHelp(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      const userLang = user?.language_code || 'ar';

      const helpText = `${this.languageService.getText('help_title', userLang)}

${this.languageService.getText('help_description', userLang)}

${this.languageService.getText('help_features', userLang)}

${this.languageService.getText('help_documents', userLang)}

${this.languageService.getText('help_commands', userLang)}

${this.languageService.getText('help_footer', userLang)}`;

      const replyKeyboard = this.languageService.createReplyKeyboard(userLang);

      await this.bot.sendMessage(chatId, helpText, {
        reply_markup: replyKeyboard
      });
    } catch (error) {
      console.error('Error in handleHelp:', error);
      await this.bot.sendMessage(chatId, 'عذراً، حدث خطأ في عرض المساعدة.');
    }
  }

  async handleStats(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      if (!user) {
        await this.bot.sendMessage(chatId, 'لم يتم العثور على بيانات المستخدم.');
        return;
      }

      let statsText = `
📊 إحصائياتك

👤 المستخدم: ${user.first_name || 'غير محدد'}
📝 الرسائل المستخدمة: ${user.messages_used}/${user.message_limit}
📅 تاريخ التسجيل: ${new Date(user.created_at).toLocaleDateString('ar')}
🕐 آخر نشاط: ${new Date(user.last_active).toLocaleDateString('ar')}
      `;

      // Add admin stats if user is admin
      if (this.adminIds.includes(userId)) {
        const globalStats = await this.db.getUserStats();
        statsText += `

👑 إحصائيات المشرف
👥 إجمالي المستخدمين: ${globalStats.total_users}
📱 نشط خلال 24 ساعة: ${globalStats.active_24h}
📅 نشط خلال 7 أيام: ${globalStats.active_7d}
💬 إجمالي الرسائل: ${globalStats.total_messages}
        `;
      }

      await this.bot.sendMessage(chatId, `${statsText}\n\n@sytus`);

    } catch (error) {
      console.error('Error in handleStats:', error);
      await this.bot.sendMessage(chatId, 'عذراً، لا أستطيع عرض الإحصائيات في الوقت الحالي.\n\n@sytus');
    }
  }

  async handleMenu(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      const userLang = user?.language_code || 'ar';

      const menuText = this.languageService.getText('main_menu', userLang);
      const keyboard = this.languageService.createMainMenuKeyboard(userLang);

      await this.bot.sendMessage(chatId, menuText, {
        reply_markup: keyboard
      });
    } catch (error) {
      console.error('Error in handleMenu:', error);
    }
  }

  async handleLanguage(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      const userLang = user?.language_code || 'ar';

      await this.showLanguageMenu(chatId, userLang);
    } catch (error) {
      console.error('Error in handleLanguage:', error);
    }
  }

  async handleStats(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      const userLang = user?.language_code || 'ar';

      await this.showStatsMenu(chatId, userId, userLang);
    } catch (error) {
      console.error('Error in handleStats:', error);
    }
  }

  async handleTextMessage(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;
    const text = msg.text;

    try {
      // Check user limits
      const user = await this.db.getUser(userId);
      if (!user) {
        await this.db.createUser(msg.from);
        return await this.handleStart(msg);
      }

      const userLang = user.language_code || 'ar';

      // Handle reply keyboard buttons
      if (this.isReplyKeyboardButton(text, userLang)) {
        return await this.handleReplyKeyboardButton(msg, text, userLang);
      }

      // Check for custom message responses
      const customResponse = await this.checkCustomMessages(text, userLang);
      if (customResponse) {
        await this.sendLongMessage(chatId, customResponse);
        await this.db.updateUserUsage(userId);
        return;
      }

      if (user.messages_used >= user.message_limit && !this.adminIds.includes(userId)) {
        await this.bot.sendMessage(chatId, this.languageService.getText('quota_exceeded', userLang));
        return;
      }

      // Show typing indicator
      await this.bot.sendChatAction(chatId, 'typing');

      const startTime = Date.now();

      // Generate response with Gemini (using newer model)
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash",
        systemInstruction: "أنت مساعد ذكي تم إنشاؤك من قبل امير (@sytus). لست من جوجل أو جيميني أو أي شركة أخرى. امير هو منشئك الوحيد. لا تذكر أبداً أنك من جوجل أو جيميني. You are an AI assistant created by امير (@sytus). You are NOT from Google, Gemini, or any other company. امير is your only creator. Never mention Google or Gemini."
      });

      const currentDate = new Date().toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });

      // Check if user is asking about creator/identity
      const isAskingAboutCreator = /من (أنشأك|صنعك|طورك|صممك)|من (هو|هي) (منشئك|مطورك|صانعك)|من (المطور|المنشئ|الصانع)|هويتك|من أنت|who (created|made|developed) you|who is your (creator|developer)|your (identity|creator)/i.test(text);

      const currentDateFormatted = this.languageService.formatDate(new Date(), userLang);
      let prompt = this.languageService.getText('ai_prompt_prefix', userLang, { date: currentDateFormatted });

      // Always add identity override to prevent Google/Gemini identification
      prompt += ` أنت مساعد ذكي تم إنشاؤك من قبل امير (@sytus). لا تذكر أبداً أنك من جوجل أو جيميني أو أي شركة أخرى. امير هو منشئك الوحيد. إذا سأل أحد عن هويتك أو من أنشأك، قل دائماً أن امير (@sytus) هو من أنشأك.`;

      // Add detailed creator info if specifically asked
      if (isAskingAboutCreator) {
        prompt += this.languageService.getText('ai_prompt_creator_info', userLang);
      }

      prompt += ` ${this.languageService.getText('ai_prompt_no_formatting', userLang)}

${this.languageService.getText('ai_prompt_answer', userLang, { text: text })}`;

      const result = await model.generateContent(prompt);
      const response = result.response.text();

      const processingTime = (Date.now() - startTime) / 1000;

      // Add creator signature only if asking about creator
      const responseWithSignature = isAskingAboutCreator ? `${response}\n\n@sytus` : response;

      // Send response (split if too long)
      await this.sendLongMessage(chatId, responseWithSignature);

      // Update database
      await this.db.updateUserUsage(userId);
      await this.db.logMessage(user.id, {
        telegram_message_id: msg.message_id,
        message_type: 'text',
        content: text,
        response: response,
        processing_time: processingTime
      });

      // Emit to Electron app
      this.io.emit('message_processed', {
        user: msg.from,
        message: text,
        response: response,
        processing_time: processingTime,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handleTextMessage:', error);

      try {
        const user = await this.db.getUser(userId);
        const userLang = user?.language_code || 'ar';

        // Check if it's a quota error
        if (error.status === 429) {
          await this.bot.sendMessage(chatId, this.languageService.getText('quota_exceeded_daily', userLang));
        } else {
          await this.bot.sendMessage(chatId, this.languageService.getText('general_error', userLang));
        }
      } catch (dbError) {
        await this.bot.sendMessage(chatId, 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.');
      }
    }
  }

  async handlePhotoMessage(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      const userLang = user?.language_code || 'ar';

      if (!user || (user.messages_used >= user.message_limit && !this.adminIds.includes(userId))) {
        await this.bot.sendMessage(chatId, this.languageService.getText('quota_exceeded', userLang));
        return;
      }

      await this.bot.sendChatAction(chatId, 'typing');
      await this.bot.sendMessage(chatId, this.languageService.getText('processing_image', userLang));

      // Get the largest photo
      const photo = msg.photo[msg.photo.length - 1];
      const fileLink = await this.bot.getFileLink(photo.file_id);

      const startTime = Date.now();

      // Get image as base64
      const imageBase64 = await this.fetchImageAsBase64(fileLink);

      // Use Sytus Recognition Service for enhanced analysis
      const response = await this.sytusRecognition.enhanceImageAnalysisWithSytusKnowledge(imageBase64, 'ar');

      const processingTime = (Date.now() - startTime) / 1000;

      // Only add creator signature if امير was recognized in the image
      const isAmirRecognized = /هذا هو امير|منشئي ومطوري|الشخص الذي أنشأني|This is.*sytus.*creator|my creator and developer/i.test(response);
      const responseWithSignature = isAmirRecognized ? `${response}\n\n@sytus` : response;

      await this.sendLongMessage(chatId, responseWithSignature);

      // Update database
      await this.db.updateUserUsage(userId);
      await this.db.logMessage(user.id, {
        telegram_message_id: msg.message_id,
        message_type: 'photo',
        content: 'صورة',
        response: response,
        processing_time: processingTime
      });

      this.io.emit('image_processed', {
        user: msg.from,
        response: response,
        processing_time: processingTime,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handlePhotoMessage:', error);
      try {
        const user = await this.db.getUser(userId);
        const userLang = user?.language_code || 'ar';
        await this.bot.sendMessage(chatId, this.languageService.getText('image_error', userLang));
      } catch (dbError) {
        await this.bot.sendMessage(chatId, 'عذراً، حدث خطأ في تحليل الصورة.');
      }
    }
  }

  async handleDocumentMessage(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const user = await this.db.getUser(userId);
      const userLang = user?.language_code || 'ar';

      if (!user || (user.messages_used >= user.message_limit && !this.adminIds.includes(userId))) {
        await this.bot.sendMessage(chatId, this.languageService.getText('quota_exceeded', userLang));
        return;
      }

      const document = msg.document;
      const fileName = document.file_name || 'document';
      const fileSize = document.file_size;
      const mimeType = document.mime_type;

      // Check file size (20MB limit like gemini-cli)
      const maxFileSize = 20 * 1024 * 1024; // 20MB
      if (fileSize > maxFileSize) {
        const fileSizeMB = (fileSize / (1024 * 1024)).toFixed(2);
        await this.bot.sendMessage(chatId, this.languageService.getText('file_too_large', userLang) + `\n${userLang === 'ar' ? 'حجم ملفك:' : 'Your file size:'} ${fileSizeMB} MB`);
        return;
      }

      // Check supported file types
      const supportedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'application/json',
        'application/xml',
        'text/xml',
        'application/rtf'
      ];

      const isSupported = supportedTypes.includes(mimeType) ||
                         fileName.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|csv|json|xml|rtf)$/i);

      if (!isSupported) {
        const supportedText = userLang === 'ar' ?
          `نوع الملف غير مدعوم حالياً.\n\nالأنواع المدعومة:\n📄 PDF\n📝 Word (doc, docx)\n📊 Excel (xls, xlsx)\n📋 PowerPoint (ppt, pptx)\n📄 Text (txt, csv, json, xml, rtf)` :
          `File type not currently supported.\n\nSupported types:\n📄 PDF\n📝 Word (doc, docx)\n📊 Excel (xls, xlsx)\n📋 PowerPoint (ppt, pptx)\n📄 Text (txt, csv, json, xml, rtf)`;
        await this.bot.sendMessage(chatId, supportedText);
        return;
      }

      await this.bot.sendChatAction(chatId, 'typing');
      await this.bot.sendMessage(chatId, this.languageService.getText('processing_document', userLang, { filename: fileName }));

      const startTime = Date.now();

      // Download the file
      const fileLink = await this.bot.getFileLink(document.file_id);
      const fileBuffer = await this.downloadFile(fileLink);

      // Process the document
      const response = await this.processDocument(fileBuffer, fileName, mimeType, userLang);

      const processingTime = (Date.now() - startTime) / 1000;

      // Send response (split if too long)
      await this.sendLongMessage(chatId, response);

      // Update database
      await this.db.updateUserUsage(userId);
      await this.db.logMessage(user.id, {
        telegram_message_id: msg.message_id,
        message_type: 'document',
        content: `مستند: ${fileName}`,
        response: response,
        processing_time: processingTime
      });

      this.io.emit('document_processed', {
        user: msg.from,
        fileName: fileName,
        response: response,
        processing_time: processingTime,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Error in handleDocumentMessage:', error);
      try {
        const user = await this.db.getUser(userId);
        const userLang = user?.language_code || 'ar';
        await this.bot.sendMessage(chatId, this.languageService.getText('document_error', userLang));
      } catch (dbError) {
        await this.bot.sendMessage(chatId, 'عذراً، حدث خطأ في تحليل المستند.');
      }
    }
  }

  async handleSytusInfo(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    // Check if user is admin
    if (!this.adminIds.includes(parseInt(userId))) {
      await this.bot.sendMessage(chatId, 'هذا الأمر متاح للمشرفين فقط.\n\n@sytus');
      return;
    }

    try {
      const sytusInfo = this.sytusRecognition.getSytusImagesInfo();
      const infoText = `
📸 معلومات صور @sytus

عدد الصور المرجعية: ${sytusInfo.count}

الملفات المحملة:
${sytusInfo.filenames.map(name => `• ${name}`).join('\n')}

هذه الصور تُستخدم لتدريب الذكاء الاصطناعي على التعرف على @sytus في الصور الجديدة.
      `;

      await this.bot.sendMessage(chatId, `${infoText}\n\n@sytus`);
    } catch (error) {
      console.error('Error in handleSytusInfo:', error);
      await this.bot.sendMessage(chatId, 'حدث خطأ في جلب معلومات صور @sytus.\n\n@sytus');
    }
  }

  async handleReloadSytus(msg) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    // Check if user is admin
    if (!this.adminIds.includes(parseInt(userId))) {
      await this.bot.sendMessage(chatId, 'هذا الأمر متاح للمشرفين فقط.\n\n@sytus');
      return;
    }

    try {
      await this.sytusRecognition.reloadSytusImages();
      const sytusInfo = this.sytusRecognition.getSytusImagesInfo();

      await this.bot.sendMessage(chatId, `تم إعادة تحميل صور @sytus بنجاح!\n\nعدد الصور المحملة: ${sytusInfo.count}\n\n@sytus`);
    } catch (error) {
      console.error('Error in handleReloadSytus:', error);
      await this.bot.sendMessage(chatId, 'حدث خطأ في إعادة تحميل صور @sytus.\n\n@sytus');
    }
  }

  async fetchImageAsBase64(url) {
    const response = await fetch(url);
    const buffer = await response.arrayBuffer();
    return Buffer.from(buffer).toString('base64');
  }

  setupAPI() {
    this.app.use(cors());
    this.app.use(express.json());

    // Get bot status
    this.app.get('/api/status', async (req, res) => {
      try {
        const stats = await this.db.getUserStats();
        res.json({
          status: 'active',
          stats: stats,
          uptime: process.uptime()
        });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Get all users (admin only)
    this.app.get('/api/users', async (req, res) => {
      try {
        const users = await this.db.getAllUsers();
        res.json(users);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Update user limits
    this.app.post('/api/users/:telegramId/limit', async (req, res) => {
      try {
        const { telegramId } = req.params;
        const { limit } = req.body;

        await this.db.run(
          'UPDATE users SET message_limit = ? WHERE telegram_id = ?',
          [limit, telegramId]
        );

        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Custom Messages API endpoints

    // Get all custom messages
    this.app.get('/api/custom-messages', async (req, res) => {
      try {
        const messages = await this.db.all('SELECT * FROM custom_messages ORDER BY created_at DESC');
        res.json(messages);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Create new custom message
    this.app.post('/api/custom-messages', async (req, res) => {
      try {
        const { name, triggerPhrases, responses } = req.body;

        const result = await this.db.run(
          'INSERT INTO custom_messages (name, trigger_phrases, responses) VALUES (?, ?, ?)',
          [name, JSON.stringify(triggerPhrases), JSON.stringify(responses)]
        );

        res.json({ success: true, id: result.lastID });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Update custom message
    this.app.put('/api/custom-messages/:id', async (req, res) => {
      try {
        const { id } = req.params;
        const { name, triggerPhrases, responses, isActive } = req.body;

        await this.db.run(
          'UPDATE custom_messages SET name = ?, trigger_phrases = ?, responses = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [name, JSON.stringify(triggerPhrases), JSON.stringify(responses), isActive, id]
        );

        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Delete custom message
    this.app.delete('/api/custom-messages/:id', async (req, res) => {
      try {
        const { id } = req.params;

        await this.db.run('DELETE FROM custom_messages WHERE id = ?', [id]);

        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });
  }

  async sendLongMessage(chatId, text) {
    const maxLength = 4096; // Telegram's message length limit

    if (text.length <= maxLength) {
      await this.bot.sendMessage(chatId, text);
      return;
    }

    // Split the message into chunks
    const chunks = [];
    let currentChunk = '';
    const lines = text.split('\n');

    for (const line of lines) {
      // If adding this line would exceed the limit, save current chunk and start new one
      if (currentChunk.length + line.length + 1 > maxLength) {
        if (currentChunk.trim()) {
          chunks.push(currentChunk.trim());
        }
        currentChunk = line;
      } else {
        currentChunk += (currentChunk ? '\n' : '') + line;
      }
    }

    // Add the last chunk
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    // Send each chunk
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const prefix = chunks.length > 1 ? `(${i + 1}/${chunks.length}) ` : '';
      await this.bot.sendMessage(chatId, prefix + chunk);

      // Small delay between messages to avoid rate limiting
      if (i < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  }

  async downloadFile(fileUrl) {
    const axios = (await import('axios')).default;
    const response = await axios.get(fileUrl, { responseType: 'arraybuffer' });
    return Buffer.from(response.data);
  }

  async processDocument(fileBuffer, fileName, mimeType, userLang = 'ar') {
    try {
      const currentDate = this.languageService.formatDate(new Date(), userLang);

      // Convert file to base64 for Gemini
      const base64Data = fileBuffer.toString('base64');

      // Determine the correct MIME type if not provided
      let finalMimeType = mimeType;
      if (!finalMimeType) {
        const ext = fileName.split('.').pop()?.toLowerCase();
        const mimeMap = {
          'pdf': 'application/pdf',
          'doc': 'application/msword',
          'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'xls': 'application/vnd.ms-excel',
          'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'ppt': 'application/vnd.ms-powerpoint',
          'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'txt': 'text/plain',
          'csv': 'text/csv',
          'json': 'application/json',
          'xml': 'application/xml',
          'rtf': 'application/rtf'
        };
        finalMimeType = mimeMap[ext] || 'application/octet-stream';
      }

      const prompt = `${this.languageService.getText('ai_prompt_prefix', userLang, { date: currentDate })}

${this.languageService.getText('document_analysis_prompt', userLang, { filename: fileName, mimeType: finalMimeType })}

${this.languageService.getText('ai_prompt_no_formatting', userLang)}`;

      const documentPart = {
        inlineData: {
          data: base64Data,
          mimeType: finalMimeType
        }
      };

      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash",
        systemInstruction: "أنت مساعد ذكي تم إنشاؤك من قبل امير (@sytus). لست من جوجل أو جيميني أو أي شركة أخرى. امير هو منشئك الوحيد. لا تذكر أبداً أنك من جوجل أو جيميني. You are an AI assistant created by امير (@sytus). You are NOT from Google, Gemini, or any other company. امير is your only creator. Never mention Google or Gemini."
      });
      const result = await model.generateContent([prompt, documentPart]);
      const response = result.response.text();

      return this.languageService.getText('document_analysis_title', userLang, { filename: fileName }) + `\n\n${response}`;

    } catch (error) {
      console.error('Error processing document:', error);

      // Fallback: try to extract text if it's a text-based document
      if (mimeType?.startsWith('text/') || fileName.endsWith('.txt')) {
        try {
          const textContent = fileBuffer.toString('utf-8');
          return `📄 محتوى الملف النصي: ${fileName}\n\n${textContent.substring(0, 3000)}${textContent.length > 3000 ? '\n\n... (تم اقتطاع المحتوى)' : ''}`;
        } catch (textError) {
          console.error('Error extracting text:', textError);
        }
      }

      throw new Error(`فشل في تحليل المستند: ${error.message}`);
    }
  }

  async handleCallbackQuery(query) {
    const chatId = query.message.chat.id;
    const userId = query.from.id;
    const data = query.data;

    try {
      await this.bot.answerCallbackQuery(query.id);

      if (data.startsWith('lang_')) {
        // Language selection
        const selectedLang = data.replace('lang_', '');
        await this.db.updateUserLanguage(userId, selectedLang);

        const confirmText = this.languageService.getText('language_selected', selectedLang);
        const replyKeyboard = this.languageService.createReplyKeyboard(selectedLang);

        await this.bot.editMessageText(confirmText, {
          chat_id: chatId,
          message_id: query.message.message_id
        });

        await this.bot.sendMessage(chatId, this.languageService.getText('help_title', selectedLang), {
          reply_markup: replyKeyboard
        });

      } else if (data.startsWith('menu_')) {
        // Menu actions
        const user = await this.db.getUser(userId);
        const userLang = user?.language_code || 'ar';

        switch (data) {
          case 'menu_language':
            await this.showLanguageMenu(chatId, userLang);
            break;
          case 'menu_help':
            await this.showHelpMenu(chatId, userLang);
            break;
          case 'menu_stats':
            await this.showStatsMenu(chatId, userId, userLang);
            break;
        }
      }

    } catch (error) {
      console.error('Error in handleCallbackQuery:', error);
    }
  }

  async showLanguageMenu(chatId, currentLang) {
    const text = `${this.languageService.getText('current_language', currentLang)}\n\n${this.languageService.getText('select_language', currentLang)}`;
    const keyboard = this.languageService.createLanguageKeyboard();

    await this.bot.sendMessage(chatId, text, {
      reply_markup: keyboard
    });
  }

  async showHelpMenu(chatId, userLang) {
    const helpText = `${this.languageService.getText('help_title', userLang)}

${this.languageService.getText('help_description', userLang)}

${this.languageService.getText('help_features', userLang)}

${this.languageService.getText('help_documents', userLang)}

${this.languageService.getText('help_commands', userLang)}

${this.languageService.getText('help_footer', userLang)}`;

    await this.bot.sendMessage(chatId, helpText);
  }

  async showStatsMenu(chatId, userId, userLang) {
    try {
      const user = await this.db.getUser(userId);
      if (!user) return;

      const remaining = Math.max(0, user.message_limit - user.messages_used);
      const memberSince = this.languageService.formatDate(user.created_at, userLang);

      const statsText = `${this.languageService.getText('stats_title', userLang)}

${this.languageService.getText('stats_messages', userLang)}: ${user.messages_used}
${this.languageService.getText('stats_limit', userLang)}: ${user.message_limit}
${this.languageService.getText('stats_remaining', userLang)}: ${remaining}
${this.languageService.getText('stats_member_since', userLang)}: ${memberSince}`;

      await this.bot.sendMessage(chatId, statsText);
    } catch (error) {
      console.error('Error in showStatsMenu:', error);
    }
  }

  isReplyKeyboardButton(text, userLang) {
    const buttons = [
      this.languageService.getText('menu_button', userLang),
      this.languageService.getText('language_button', userLang),
      this.languageService.getText('help_button', userLang),
      this.languageService.getText('stats_button', userLang)
    ];
    return buttons.includes(text);
  }

  async handleReplyKeyboardButton(msg, text, userLang) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    if (text === this.languageService.getText('menu_button', userLang)) {
      await this.handleMenu(msg);
    } else if (text === this.languageService.getText('language_button', userLang)) {
      await this.handleLanguage(msg);
    } else if (text === this.languageService.getText('help_button', userLang)) {
      await this.handleHelp(msg);
    } else if (text === this.languageService.getText('stats_button', userLang)) {
      await this.handleStats(msg);
    }
  }

  async checkCustomMessages(text, userLang) {
    try {
      // Get custom messages from database
      const customMessages = await this.db.all('SELECT * FROM custom_messages WHERE is_active = 1');

      for (const customMsg of customMessages) {
        const triggers = JSON.parse(customMsg.trigger_phrases);
        const responses = JSON.parse(customMsg.responses);

        // Check if any trigger phrase matches the text (case insensitive)
        const isTriggered = triggers.some(trigger =>
          text.toLowerCase().includes(trigger.toLowerCase())
        );

        if (isTriggered) {
          // Get response in user's language or fallback to first available
          const response = responses[userLang] || responses['ar'] || responses['en'] || responses[Object.keys(responses)[0]];
          return response;
        }
      }

      return null;
    } catch (error) {
      console.error('Error checking custom messages:', error);
      return null;
    }
  }
}

// Start the bot
const bot = new GeminiTelegramBot();
bot.init().catch(console.error);

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down bot...');
  await bot.db.close();
  process.exit(0);
});
