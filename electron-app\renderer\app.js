const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Global variables
let currentUser = null;
let messagesChart = null;
let usersChart = null;

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
});

async function initializeApp() {
    setupNavigation();
    await loadDashboard();
    startRealTimeUpdates();
}

// Navigation setup
function setupNavigation() {
    const menuItems = document.querySelectorAll('.menu-item');
    
    menuItems.forEach(item => {
        item.addEventListener('click', () => {
            const page = item.dataset.page;
            switchPage(page);
            
            // Update active menu item
            menuItems.forEach(mi => mi.classList.remove('active'));
            item.classList.add('active');
        });
    });
}

function switchPage(pageId) {
    // Hide all pages
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    
    // Show selected page
    document.getElementById(pageId).classList.add('active');
    
    // Load page-specific data
    switch(pageId) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'accounts':
            loadAccountsData();
            break;
        case 'users':
            loadUsers();
            break;
        case 'analytics':
            loadAnalytics();
            break;
        case 'settings':
            loadSettings();
            break;
        case 'custom-messages':
            loadCustomMessages();
            break;
        case 'logs':
            loadLogs();
            break;
    }
}

// Dashboard functions
async function loadDashboard() {
    try {
        const data = await ipcRenderer.invoke('get-dashboard-data');

        // Update stats
        document.getElementById('totalUsers').textContent = data.userStats.total_users || 0;
        document.getElementById('activeUsers24h').textContent = data.userStats.active_24h || 0;
        document.getElementById('activeUsers7d').textContent = data.userStats.active_7d || 0;
        document.getElementById('totalMessages').textContent = data.userStats.total_messages || 0;

        // Update bot status
        await loadBotStatus();

        // Load recent users table
        loadRecentUsersTable(data.recentUsers);

        // Load recent messages
        loadRecentMessages(data.recentMessages);

    } catch (error) {
        console.error('Error loading dashboard:', error);
        showNotification('Error loading dashboard data', 'error');
    }
}

async function loadBotStatus() {
    try {
        const status = await ipcRenderer.invoke('get-bot-status');

        if (status.running) {
            updateBotStatus('running');
            document.getElementById('startBotBtn').style.display = 'none';
            document.getElementById('stopBotBtn').style.display = 'inline-flex';

            // Update bot details
            document.getElementById('botUptime').textContent = formatUptime(status.uptime);
            document.getElementById('botPort').textContent = '3001';
        } else {
            updateBotStatus('stopped');
            document.getElementById('startBotBtn').style.display = 'inline-flex';
            document.getElementById('stopBotBtn').style.display = 'none';

            // Update bot details
            document.getElementById('botUptime').textContent = '0s';
            document.getElementById('botPort').textContent = '3001';
        }

        // Update active account info
        const accountsData = await ipcRenderer.invoke('get-accounts-data');
        const activeAccount = accountsData.activeAccount;
        document.getElementById('botActiveAccount').textContent =
            activeAccount ? activeAccount.email : 'None';

    } catch (error) {
        console.error('Error loading bot status:', error);
        updateBotStatus('error');
    }
}

function loadRecentUsersTable(users) {
    const tbody = document.querySelector('#recentUsersTable tbody');
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.first_name || 'N/A'}</td>
            <td>@${user.username || 'N/A'}</td>
            <td>${user.messages_used}/${user.message_limit}</td>
            <td>${formatDate(user.last_active)}</td>
        `;
        tbody.appendChild(row);
    });
}

function loadRecentMessages(messages) {
    const container = document.getElementById('recentMessages');
    container.innerHTML = '';
    
    messages.forEach(message => {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message-item';
        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-user">${message.first_name || 'Unknown'}</div>
                <div class="message-text">${truncateText(message.content || 'Media message', 50)}</div>
                <div class="message-time">${formatDate(message.created_at)}</div>
            </div>
        `;
        container.appendChild(messageDiv);
    });
}

// Users management
async function loadUsers() {
    try {
        const users = await ipcRenderer.invoke('get-users');
        const tbody = document.querySelector('#usersTable tbody');
        tbody.innerHTML = '';
        
        users.forEach(user => {
            const row = document.createElement('tr');
            const statusBadge = user.is_banned ? 
                '<span class="badge banned">Banned</span>' : 
                '<span class="badge active">Active</span>';
            
            row.innerHTML = `
                <td>${user.telegram_id}</td>
                <td>${user.first_name || 'N/A'}</td>
                <td>@${user.username || 'N/A'}</td>
                <td>${user.messages_used}</td>
                <td>${user.message_limit}</td>
                <td>${statusBadge}</td>
                <td class="user-actions">
                    <button class="btn btn-sm btn-primary" onclick="editUser('${user.telegram_id}', ${user.message_limit}, ${user.is_banned})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="resetUserUsage('${user.telegram_id}')">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button class="btn btn-sm ${user.is_banned ? 'btn-success' : 'btn-danger'}" 
                            onclick="toggleUserBan('${user.telegram_id}', ${!user.is_banned})">
                        <i class="fas ${user.is_banned ? 'fa-unlock' : 'fa-ban'}"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
        
    } catch (error) {
        console.error('Error loading users:', error);
        showNotification('Error loading users', 'error');
    }
}

function editUser(telegramId, currentLimit, isBanned) {
    currentUser = telegramId;
    document.getElementById('editUserLimit').value = currentLimit;
    document.getElementById('editUserBanned').checked = isBanned;
    showModal('userModal');
}

async function saveUserChanges() {
    if (!currentUser) return;
    
    const newLimit = parseInt(document.getElementById('editUserLimit').value);
    const isBanned = document.getElementById('editUserBanned').checked;
    
    try {
        await ipcRenderer.invoke('update-user-limit', currentUser, newLimit);
        await ipcRenderer.invoke('toggle-user-ban', currentUser, isBanned);
        
        closeModal('userModal');
        loadUsers();
        showNotification('User updated successfully', 'success');
        
    } catch (error) {
        console.error('Error updating user:', error);
        showNotification('Error updating user', 'error');
    }
}

async function resetUserUsage(telegramId) {
    try {
        await ipcRenderer.invoke('reset-user-usage', telegramId);
        loadUsers();
        showNotification('User usage reset successfully', 'success');
    } catch (error) {
        console.error('Error resetting user usage:', error);
        showNotification('Error resetting user usage', 'error');
    }
}

async function toggleUserBan(telegramId, isBanned) {
    try {
        await ipcRenderer.invoke('toggle-user-ban', telegramId, isBanned);
        loadUsers();
        showNotification(`User ${isBanned ? 'banned' : 'unbanned'} successfully`, 'success');
    } catch (error) {
        console.error('Error toggling user ban:', error);
        showNotification('Error updating user status', 'error');
    }
}

// Analytics
async function loadAnalytics() {
    const days = parseInt(document.getElementById('analyticsRange').value);
    
    try {
        const analytics = await ipcRenderer.invoke('get-analytics', days);
        
        // Prepare data for charts
        const dates = analytics.map(a => a.date).reverse();
        const messageCounts = analytics.map(a => a.message_count).reverse();
        const userCounts = analytics.map(a => a.unique_users).reverse();
        
        // Update messages chart
        updateMessagesChart(dates, messageCounts);
        
        // Update users chart
        updateUsersChart(dates, userCounts);
        
    } catch (error) {
        console.error('Error loading analytics:', error);
        showNotification('Error loading analytics', 'error');
    }
}

function updateMessagesChart(labels, data) {
    const ctx = document.getElementById('messagesChart').getContext('2d');
    
    if (messagesChart) {
        messagesChart.destroy();
    }
    
    messagesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Messages',
                data: data,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateUsersChart(labels, data) {
    const ctx = document.getElementById('usersChart').getContext('2d');
    
    if (usersChart) {
        usersChart.destroy();
    }
    
    usersChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Active Users',
                data: data,
                backgroundColor: '#764ba2',
                borderColor: '#667eea',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Settings
async function loadSettings() {
    try {
        const settings = await ipcRenderer.invoke('get-settings');
        
        document.getElementById('defaultUserLimit').value = settings.default_user_limit || 50;
        document.getElementById('welcomeMessageAr').value = settings.welcome_message_ar || '';
        document.getElementById('welcomeMessageEn').value = settings.welcome_message_en || '';
        document.getElementById('maintenanceMode').checked = settings.maintenance_mode === 'true';
        
        // Update bot status in settings
        const dashboardData = await ipcRenderer.invoke('get-dashboard-data');
        document.getElementById('settingsBotStatus').textContent = dashboardData.botStatus;
        
    } catch (error) {
        console.error('Error loading settings:', error);
        showNotification('Error loading settings', 'error');
    }
}

async function saveSettings() {
    try {
        const settings = {
            default_user_limit: document.getElementById('defaultUserLimit').value,
            welcome_message_ar: document.getElementById('welcomeMessageAr').value,
            welcome_message_en: document.getElementById('welcomeMessageEn').value,
            maintenance_mode: document.getElementById('maintenanceMode').checked.toString()
        };
        
        for (const [key, value] of Object.entries(settings)) {
            await ipcRenderer.invoke('update-setting', key, value);
        }
        
        showNotification('Settings saved successfully', 'success');
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showNotification('Error saving settings', 'error');
    }
}

// Logs
function loadLogs() {
    // This would typically load from a log file or database
    // For now, we'll show some sample logs
    const logsContainer = document.getElementById('logsContainer');
    logsContainer.innerHTML = `
        <div class="log-entry">
            <span class="log-time">[${new Date().toISOString()}]</span>
            <span class="log-level info">INFO</span>
            <span class="log-message">Application started successfully</span>
        </div>
        <div class="log-entry">
            <span class="log-time">[${new Date().toISOString()}]</span>
            <span class="log-level info">INFO</span>
            <span class="log-message">Database connection established</span>
        </div>
    `;
}

// Utility functions
function updateBotStatus(status) {
    const indicator = document.getElementById('botStatus');
    const text = document.getElementById('botStatusText');
    
    indicator.className = 'status-indicator';
    
    switch(status) {
        case 'active':
            indicator.classList.add('online');
            text.textContent = 'Online';
            break;
        case 'offline':
            indicator.classList.add('offline');
            text.textContent = 'Offline';
            break;
        default:
            text.textContent = 'Unknown';
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function formatUptime(seconds) {
    if (!seconds || seconds < 1) return '0s';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
        return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
    } else {
        return `${secs}s`;
    }
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

function showModal(modalId) {
    document.getElementById(modalId).classList.add('active');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('active');
    currentUser = null;
}

function showNotification(message, type = 'info') {
    // Simple notification system - could be enhanced with a proper toast library
    console.log(`${type.toUpperCase()}: ${message}`);
    
    // You could implement a proper notification system here
    alert(message);
}

async function refreshData() {
    const activePage = document.querySelector('.page.active').id;
    
    switch(activePage) {
        case 'dashboard':
            await loadDashboard();
            break;
        case 'users':
            await loadUsers();
            break;
        case 'analytics':
            await loadAnalytics();
            break;
        case 'settings':
            await loadSettings();
            break;
    }
    
    showNotification('Data refreshed', 'success');
}

function searchUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const rows = document.querySelectorAll('#usersTable tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

function clearLogs() {
    document.getElementById('logsContainer').innerHTML = '';
    showNotification('Logs cleared', 'success');
}

// Real-time updates
function startRealTimeUpdates() {
    // Update dashboard every 30 seconds
    setInterval(async () => {
        if (document.querySelector('.page.active').id === 'dashboard') {
            await loadDashboard();
        }
    }, 30000);
}

// Bot Management Functions
async function startBot() {
    const startBtn = document.getElementById('startBotBtn');
    const stopBtn = document.getElementById('stopBotBtn');

    startBtn.disabled = true;
    startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting...';

    try {
        await ipcRenderer.invoke('start-bot');
        startBtn.style.display = 'none';
        stopBtn.style.display = 'inline-flex';
        updateBotStatus('running');
        showNotification('Bot started successfully!', 'success');
    } catch (error) {
        showNotification(`Failed to start bot: ${error.message}`, 'error');
    } finally {
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play"></i> Start Bot';
    }
}

async function stopBot() {
    const startBtn = document.getElementById('startBotBtn');
    const stopBtn = document.getElementById('stopBotBtn');

    stopBtn.disabled = true;
    stopBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Stopping...';

    try {
        await ipcRenderer.invoke('stop-bot');
        startBtn.style.display = 'inline-flex';
        stopBtn.style.display = 'none';
        updateBotStatus('stopped');
        showNotification('Bot stopped successfully!', 'success');
    } catch (error) {
        showNotification(`Failed to stop bot: ${error.message}`, 'error');
    } finally {
        stopBtn.disabled = false;
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> Stop Bot';
    }
}

async function restartBot() {
    const restartBtn = document.getElementById('restartBotBtn');

    restartBtn.disabled = true;
    restartBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Restarting...';

    try {
        await ipcRenderer.invoke('restart-bot');
        updateBotStatus('running');
        showNotification('Bot restarted successfully!', 'success');
    } catch (error) {
        showNotification(`Failed to restart bot: ${error.message}`, 'error');
    } finally {
        restartBtn.disabled = false;
        restartBtn.innerHTML = '<i class="fas fa-redo"></i> Restart';
    }
}

async function openAccountsWindow() {
    switchPage('accounts');

    // Update active menu item
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(mi => mi.classList.remove('active'));
    document.querySelector('[data-page="accounts"]').classList.add('active');
}

function updateBotStatus(status) {
    const dot = document.getElementById('botStatusDot');
    const text = document.getElementById('botStatusText');
    const card = document.getElementById('botStatusCard');

    dot.className = 'status-dot';
    card.className = 'bot-status-card';

    switch(status) {
        case 'running':
            dot.classList.add('online');
            text.textContent = 'Running';
            card.classList.add('bot-running');
            break;
        case 'stopped':
            dot.classList.add('offline');
            text.textContent = 'Stopped';
            card.classList.add('bot-stopped');
            break;
        case 'error':
            dot.classList.add('error');
            text.textContent = 'Error';
            card.classList.add('bot-error');
            break;
        default:
            dot.classList.add('unknown');
            text.textContent = 'Unknown';
    }
}

// Accounts Management Functions
async function addNewAccount() {
    showLoadingMain('Authenticating with Google...');
    try {
        await ipcRenderer.invoke('add-google-account');
        await loadAccountsData();
    } catch (error) {
        showNotification(`Failed to add account: ${error.message}`, 'error');
    } finally {
        hideLoadingMain();
    }
}

async function refreshAccountsData() {
    try {
        await loadAccountsData();
        showNotification('Accounts data refreshed!', 'success');
    } catch (error) {
        showNotification(`Failed to refresh accounts: ${error.message}`, 'error');
    }
}

async function loadAccountsData() {
    try {
        const accountsData = await ipcRenderer.invoke('get-accounts-data');
        updateAccountsUI(accountsData);
    } catch (error) {
        console.error('Error loading accounts data:', error);
        showNotification('Error loading accounts data', 'error');
    }
}

function updateAccountsUI(data) {
    const { stats, accounts, activeAccount } = data;

    // Update statistics
    document.getElementById('totalAccountsCount').textContent = stats.totalAccounts;
    document.getElementById('totalQuotaUsedCount').textContent = stats.totalQuotaUsed;
    document.getElementById('totalQuotaLimitCount').textContent = stats.totalQuotaLimit;
    document.getElementById('quotaPercentageCount').textContent = `${Math.round(stats.quotaPercentage)}%`;

    // Update active account
    updateActiveAccountUIMain(activeAccount);

    // Update accounts grid
    updateAccountsGridMain(accounts);
}

function updateActiveAccountUIMain(activeAccount) {
    const picture = document.getElementById('activeAccountPictureMain');
    const name = document.getElementById('activeAccountNameMain');
    const email = document.getElementById('activeAccountEmailMain');
    const quota = document.getElementById('activeAccountQuotaMain');
    const quotaBar = document.getElementById('activeAccountQuotaBarMain');

    if (activeAccount) {
        picture.src = activeAccount.picture || 'https://via.placeholder.com/50';
        picture.style.display = 'block';
        name.textContent = activeAccount.name;
        email.textContent = activeAccount.email;
        quota.textContent = `${activeAccount.quotaUsed}/${activeAccount.quotaLimit} requests used`;

        const percentage = (activeAccount.quotaUsed / activeAccount.quotaLimit) * 100;
        quotaBar.style.width = `${percentage}%`;
        quotaBar.className = `quota-fill ${percentage > 80 ? 'high' : percentage > 60 ? 'medium' : 'low'}`;
    } else {
        picture.style.display = 'none';
        name.textContent = 'No active account';
        email.textContent = 'Please add and select an account';
        quota.textContent = '0/0 requests used';
        quotaBar.style.width = '0%';
    }
}

function updateAccountsGridMain(accounts) {
    const grid = document.getElementById('accountsGridMain');
    grid.innerHTML = '';

    if (accounts.length === 0) {
        grid.innerHTML = `
            <div class="no-accounts">
                <i class="fas fa-user-plus"></i>
                <h3>No accounts added</h3>
                <p>Add your first Google account to get started</p>
                <button class="btn btn-primary" onclick="addNewAccount()">
                    <i class="fas fa-plus"></i> Add Account
                </button>
            </div>
        `;
        return;
    }

    accounts.forEach(account => {
        const accountCard = createAccountCardMain(account);
        grid.appendChild(accountCard);
    });
}

function createAccountCardMain(account) {
    const card = document.createElement('div');
    card.className = `account-card ${account.isActive ? 'active' : ''}`;

    const quotaPercentage = (account.requestsToday / account.quotaLimit) * 100;
    const statusClass = account.hasCredentials ? 'connected' : 'error';

    card.innerHTML = `
        <div class="account-header">
            <img src="${account.picture || 'https://via.placeholder.com/40'}" alt="Profile" class="account-avatar">
            <div class="account-info">
                <h4>${account.name}</h4>
                <p>${account.email}</p>
            </div>
            <div class="account-status ${statusClass}">
                <i class="fas ${account.hasCredentials ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
            </div>
        </div>
        <div class="account-stats">
            <div class="stat">
                <span class="stat-label">Requests Today:</span>
                <span class="stat-value">${account.requestsToday}/${account.quotaLimit}</span>
            </div>
            <div class="quota-bar">
                <div class="quota-fill ${quotaPercentage > 80 ? 'high' : quotaPercentage > 60 ? 'medium' : 'low'}"
                     style="width: ${quotaPercentage}%"></div>
            </div>
            <div class="stat">
                <span class="stat-label">Last Used:</span>
                <span class="stat-value">${formatDate(account.lastUsed)}</span>
            </div>
        </div>
        <div class="account-actions">
            ${!account.isActive ? `
                <button class="btn btn-sm btn-primary" onclick="setActiveAccountMain('${account.id}')">
                    <i class="fas fa-check"></i> Set Active
                </button>
            ` : `
                <span class="active-badge"><i class="fas fa-star"></i> Active</span>
            `}
            <button class="btn btn-sm btn-danger" onclick="removeAccountMain('${account.id}')">
                <i class="fas fa-trash"></i> Remove
            </button>
        </div>
    `;

    return card;
}

async function setActiveAccountMain(accountId) {
    try {
        await ipcRenderer.invoke('set-active-account', accountId);
        await loadAccountsData();
        showNotification('Active account updated!', 'success');
    } catch (error) {
        showNotification(`Failed to set active account: ${error.message}`, 'error');
    }
}

async function removeAccountMain(accountId) {
    if (confirm('Are you sure you want to remove this account?')) {
        try {
            await ipcRenderer.invoke('remove-account', accountId);
            await loadAccountsData();
            showNotification('Account removed successfully!', 'success');
        } catch (error) {
            showNotification(`Failed to remove account: ${error.message}`, 'error');
        }
    }
}

function showLoadingMain(message = 'Loading...') {
    const overlay = document.getElementById('loadingOverlayMain');
    const text = overlay.querySelector('p');
    text.textContent = message;
    overlay.style.display = 'flex';
}

function hideLoadingMain() {
    document.getElementById('loadingOverlayMain').style.display = 'none';
}

// Listen for IPC events
ipcRenderer.on('refresh-data', () => {
    refreshData();
});

ipcRenderer.on('navigate-to-accounts', () => {
    switchPage('accounts');

    // Update active menu item
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(mi => mi.classList.remove('active'));
    document.querySelector('[data-page="accounts"]').classList.add('active');
});

ipcRenderer.on('bot-status-changed', (event, status) => {
    updateBotStatus(status);

    const startBtn = document.getElementById('startBotBtn');
    const stopBtn = document.getElementById('stopBotBtn');

    if (status === 'running') {
        startBtn.style.display = 'none';
        stopBtn.style.display = 'inline-flex';
    } else {
        startBtn.style.display = 'inline-flex';
        stopBtn.style.display = 'none';
    }
});

ipcRenderer.on('bot-action', (event, action) => {
    console.log('Bot action:', action);
    // Handle bot start/stop/restart actions
});

// Cleanup function for bot termination
async function cleanupBot() {
    try {
        const result = await ipcRenderer.invoke('cleanup-bot');
        if (result.success) {
            console.log('Bot cleanup successful:', result.message);
        } else {
            console.error('Bot cleanup failed:', result.error);
        }
        return result;
    } catch (error) {
        console.error('Error during bot cleanup:', error);
        return { success: false, error: error.message };
    }
}

// Handle page unload - cleanup bot if needed
window.addEventListener('beforeunload', async (event) => {
    // Note: This may not always work due to browser restrictions
    // The main process handlers are more reliable
    try {
        await cleanupBot();
    } catch (error) {
        console.error('Error during page unload cleanup:', error);
    }
});

// Custom Messages Management
let customMessages = [];
const API_BASE_URL = 'http://localhost:3001/api';

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

async function loadCustomMessages() {
    try {
        const result = await ipcRenderer.invoke('get-custom-messages');

        if (result.success) {
            customMessages = result.data;
            renderCustomMessages();
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('Error loading custom messages:', error);
        const container = document.getElementById('customMessagesList');
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle fa-3x"></i>
                    <h3>Error Loading Messages</h3>
                    <p>Unable to load custom messages from database.</p>
                    <button class="btn btn-secondary" onclick="loadCustomMessages()">
                        <i class="fas fa-sync-alt"></i> Retry
                    </button>
                </div>
            `;
        }
        showNotification('Error loading custom messages: ' + error.message, 'error');
    }
}

function renderCustomMessages() {
    const container = document.getElementById('customMessagesList');

    if (customMessages.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-comments fa-3x"></i>
                <h3>No Custom Messages</h3>
                <p>No custom messages configured yet. Click "Add Custom Message" to get started.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = customMessages.map(message => {
        const triggers = JSON.parse(message.trigger_phrases);
        const responses = JSON.parse(message.responses);

        return `
            <div class="message-item">
                <div class="message-header">
                    <h4>${message.name}</h4>
                    <div class="message-actions">
                        <span class="status-badge ${message.is_active ? 'active' : 'inactive'}">
                            ${message.is_active ? 'Active' : 'Inactive'}
                        </span>
                        <button class="btn btn-sm btn-secondary" onclick="editCustomMessage(${message.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteCustomMessage(${message.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="message-content">
                    <div class="triggers">
                        <strong>Triggers:</strong>
                        ${triggers.map(trigger => `<span class="trigger-tag">${trigger}</span>`).join('')}
                    </div>
                    <div class="responses">
                        <strong>Responses:</strong>
                        ${responses.ar ? `<div class="response-item"><span class="lang-flag">🇮🇶</span> ${truncateText(responses.ar, 100)}</div>` : ''}
                        ${responses.en ? `<div class="response-item"><span class="lang-flag">🇺🇸</span> ${truncateText(responses.en, 100)}</div>` : ''}
                    </div>
                    <div class="message-meta">
                        <small>Created: ${new Date(message.created_at).toLocaleDateString()}</small>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function showAddMessageForm() {
    document.getElementById('addMessageCard').style.display = 'block';
}

function hideAddMessageForm() {
    document.getElementById('addMessageCard').style.display = 'none';
    document.getElementById('customMessageForm').reset();
}

function switchLanguageTab(lang) {
    // Remove active class from all tabs and panes
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

    // Add active class to selected tab and pane
    event.target.classList.add('active');
    document.getElementById(`tab-${lang}`).classList.add('active');
}

async function refreshCustomMessages() {
    await loadCustomMessages();
    showNotification('Custom messages refreshed!', 'success');
}

async function deleteCustomMessage(id) {
    if (!confirm('Are you sure you want to delete this custom message?')) {
        return;
    }

    try {
        const result = await ipcRenderer.invoke('delete-custom-message', id);

        if (result.success) {
            showNotification('Custom message deleted successfully!', 'success');
            loadCustomMessages();
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('Error deleting message:', error);
        showNotification('Error deleting custom message: ' + error.message, 'error');
    }
}

async function editCustomMessage(id) {
    try {
        const message = customMessages.find(m => m.id === id);
        if (!message) {
            showNotification('Message not found', 'error');
            return;
        }

        const triggers = JSON.parse(message.trigger_phrases);
        const responses = JSON.parse(message.responses);

        // Show edit modal
        showEditModal(message, triggers, responses);
    } catch (error) {
        console.error('Error editing message:', error);
        showNotification('Error editing custom message: ' + error.message, 'error');
    }
}

function showEditModal(message, triggers, responses) {
    // Create modal HTML
    const modalHTML = `
        <div class="modal-overlay" id="editModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Edit Custom Message</h3>
                    <button class="modal-close" onclick="closeEditModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editMessageForm">
                        <div class="form-group">
                            <label for="editMessageName">Message Name</label>
                            <input type="text" id="editMessageName" class="form-control" value="${message.name}" required>
                        </div>

                        <div class="form-group">
                            <label for="editTriggerPhrases">Trigger Phrases</label>
                            <textarea id="editTriggerPhrases" class="form-control" rows="3" required>${triggers.join('\n')}</textarea>
                            <small class="form-text">Enter phrases that will trigger this response (one per line)</small>
                        </div>

                        <div class="form-group">
                            <label>Responses by Language</label>
                            <div class="language-tabs">
                                <button type="button" class="tab-btn active" onclick="switchEditLanguageTab('ar')">🇮🇶 Arabic</button>
                                <button type="button" class="tab-btn" onclick="switchEditLanguageTab('en')">🇺🇸 English</button>
                            </div>
                            <div class="tab-content">
                                <div class="tab-pane active" id="edit-tab-ar">
                                    <textarea id="editResponseArabic" class="form-control" rows="4" placeholder="Enter Arabic response...">${responses.ar || ''}</textarea>
                                </div>
                                <div class="tab-pane" id="edit-tab-en">
                                    <textarea id="editResponseEnglish" class="form-control" rows="4" placeholder="Enter English response...">${responses.en || ''}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="editIsActive" ${message.is_active ? 'checked' : ''}>
                                Active
                            </label>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="closeEditModal()">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Setup form handler
    document.getElementById('editMessageForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await saveEditedMessage(message.id);
    });
}

function switchEditLanguageTab(lang) {
    // Remove active class from all tabs and panes
    document.querySelectorAll('#editModal .tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('#editModal .tab-pane').forEach(pane => pane.classList.remove('active'));

    // Add active class to selected tab and pane
    event.target.classList.add('active');
    document.getElementById(`edit-tab-${lang}`).classList.add('active');
}

async function saveEditedMessage(id) {
    try {
        const name = document.getElementById('editMessageName').value.trim();
        const triggerPhrasesText = document.getElementById('editTriggerPhrases').value.trim();
        const responseArabic = document.getElementById('editResponseArabic').value.trim();
        const responseEnglish = document.getElementById('editResponseEnglish').value.trim();
        const isActive = document.getElementById('editIsActive').checked;

        if (!name || !triggerPhrasesText) {
            showNotification('Please fill in the required fields', 'error');
            return;
        }

        if (!responseArabic && !responseEnglish) {
            showNotification('Please provide at least one response (Arabic or English)', 'error');
            return;
        }

        const triggerPhrases = triggerPhrasesText.split('\n').map(phrase => phrase.trim()).filter(phrase => phrase);
        const responses = {};

        if (responseArabic) responses.ar = responseArabic;
        if (responseEnglish) responses.en = responseEnglish;

        const result = await ipcRenderer.invoke('update-custom-message', id, {
            name,
            triggerPhrases,
            responses,
            isActive
        });

        if (result.success) {
            showNotification('Custom message updated successfully!', 'success');
            closeEditModal();
            loadCustomMessages();
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('Error saving edited message:', error);
        showNotification('Error saving changes: ' + error.message, 'error');
    }
}

function closeEditModal() {
    const modal = document.getElementById('editModal');
    if (modal) {
        modal.remove();
    }
}

// Handle custom message form submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('customMessageForm');
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const name = document.getElementById('messageName').value.trim();
            const triggerPhrasesText = document.getElementById('triggerPhrases').value.trim();
            const responseArabic = document.getElementById('responseArabic').value.trim();
            const responseEnglish = document.getElementById('responseEnglish').value.trim();

            if (!name || !triggerPhrasesText) {
                showNotification('Please fill in the required fields', 'error');
                return;
            }

            if (!responseArabic && !responseEnglish) {
                showNotification('Please provide at least one response (Arabic or English)', 'error');
                return;
            }

            const triggerPhrases = triggerPhrasesText.split('\n').map(phrase => phrase.trim()).filter(phrase => phrase);
            const responses = {};

            if (responseArabic) responses.ar = responseArabic;
            if (responseEnglish) responses.en = responseEnglish;

            try {
                const result = await ipcRenderer.invoke('save-custom-message', {
                    name,
                    triggerPhrases,
                    responses
                });

                if (result.success) {
                    showNotification('Custom message saved successfully!', 'success');
                    hideAddMessageForm();
                    loadCustomMessages();
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Error saving message:', error);
                showNotification('Error saving custom message: ' + error.message, 'error');
            }
        });
    }
});
