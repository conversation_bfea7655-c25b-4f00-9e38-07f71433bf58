/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { licensing_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof licensing_v1.Licensing;
};
export declare function licensing(version: 'v1'): licensing_v1.Licensing;
export declare function licensing(options: licensing_v1.Options): licensing_v1.Licensing;
declare const auth: AuthPlus;
export { auth };
export { licensing_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
