/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { dataflow_v1b3 } from './v1b3';
export declare const VERSIONS: {
    v1b3: typeof dataflow_v1b3.Dataflow;
};
export declare function dataflow(version: 'v1b3'): dataflow_v1b3.Dataflow;
export declare function dataflow(options: dataflow_v1b3.Options): dataflow_v1b3.Dataflow;
declare const auth: AuthPlus;
export { auth };
export { dataflow_v1b3 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
