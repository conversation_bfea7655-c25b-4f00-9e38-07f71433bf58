/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { backupdr_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof backupdr_v1.Backupdr;
};
export declare function backupdr(version: 'v1'): backupdr_v1.Backupdr;
export declare function backupdr(options: backupdr_v1.Options): backupdr_v1.Backupdr;
declare const auth: AuthPlus;
export { auth };
export { backupdr_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
