/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { ml_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof ml_v1.Ml;
};
export declare function ml(version: 'v1'): ml_v1.Ml;
export declare function ml(options: ml_v1.Options): ml_v1.Ml;
declare const auth: AuthPlus;
export { auth };
export { ml_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
