/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace firebaseappdistribution_v1alpha {
    export interface Options extends GlobalOptions {
        version: 'v1alpha';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Firebase App Distribution API
     *
     *
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const firebaseappdistribution = google.firebaseappdistribution('v1alpha');
     * ```
     */
    export class Firebaseappdistribution {
        context: APIRequestContext;
        apps: Resource$Apps;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Point for describing bounding boxes tap locations Top left is 0,0
     */
    export interface Schema$AndroidxCrawlerOutputPoint {
        xCoordinate?: number | null;
        yCoordinate?: number | null;
    }
    /**
     * App bundle test certificate
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaAabCertificate {
        /**
         * MD5 hash of the certificate used to resign the AAB
         */
        certificateHashMd5?: string | null;
        /**
         * SHA1 hash of the certificate used to resign the AAB
         */
        certificateHashSha1?: string | null;
        /**
         * SHA256 hash of the certificate used to resign the AAB
         */
        certificateHashSha256?: string | null;
    }
    /**
     * Instructions for AI driven test
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaAiInstructions {
        /**
         * Optional. Describes the app to give the AI some context
         */
        appDescription?: string | null;
        /**
         * Required. Steps to be accomplished by the AI
         */
        steps?: Schema$GoogleFirebaseAppdistroV1alphaAiStep[];
    }
    /**
     * A step to be accomplished by the AI
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaAiStep {
        /**
         * An assertion to be checked by the AI
         */
        assertion?: string | null;
        /**
         * A goal to be accomplished by the AI
         */
        goal?: string | null;
    }
    /**
     * Captures the results of an AiStep
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaAiStepResult {
        /**
         * Output only. Details for an assertion step.
         */
        assertionDetails?: Schema$GoogleFirebaseAppdistroV1alphaAssertionDetails;
        /**
         * Output only. Details for a goal step.
         */
        goalDetails?: Schema$GoogleFirebaseAppdistroV1alphaGoalDetails;
        /**
         * Output only. The current state of the step
         */
        state?: string | null;
        /**
         * Required. The step performed by the AI
         */
        step?: Schema$GoogleFirebaseAppdistroV1alphaAiStep;
    }
    export interface Schema$GoogleFirebaseAppdistroV1alphaApp {
        /**
         * App bundle test certificate generated for the app.
         */
        aabCertificate?: Schema$GoogleFirebaseAppdistroV1alphaAabCertificate;
        /**
         * App bundle state. Only valid for android apps. The app_view field in the request must be set to FULL in order for this to be populated.
         */
        aabState?: string | null;
        /**
         * Firebase gmp app id
         */
        appId?: string | null;
        /**
         * Bundle identifier
         */
        bundleId?: string | null;
        /**
         * Developer contact email for testers to reach out to about privacy or support issues.
         */
        contactEmail?: string | null;
        /**
         * iOS or Android
         */
        platform?: string | null;
        /**
         * Project number of the Firebase project, for example 300830567303.
         */
        projectNumber?: string | null;
    }
    /**
     * An app crash that occurred during an automated test.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaAppCrash {
        /**
         * Output only. The message associated with the crash.
         */
        message?: string | null;
        /**
         * Output only. The raw stack trace.
         */
        stackTrace?: string | null;
    }
    /**
     * Details for an assertion step.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaAssertionDetails {
        /**
         * Output only. An explanation justifying the assertion result.
         */
        explanation?: string | null;
        /**
         * Output only. The result of the assertion.
         */
        result?: boolean | null;
        /**
         * Output only. The screenshot used in the context of this assertion.
         */
        screenshot?: Schema$GoogleFirebaseAppdistroV1alphaScreenshot;
    }
    export interface Schema$GoogleFirebaseAppdistroV1alphaCreateReleaseNotesRequest {
        /**
         * The actual release notes body from the user
         */
        releaseNotes?: Schema$GoogleFirebaseAppdistroV1alphaReleaseNotes;
    }
    export interface Schema$GoogleFirebaseAppdistroV1alphaCreateReleaseNotesResponse {
    }
    /**
     * A high level action taken by the AI on the device, potentially involving multiple taps, text entries, waits, etc.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaDeviceAction {
        /**
         * Output only. A short description of the high level action taken by the AI agent.
         */
        description?: string | null;
        /**
         * Output only. The interactions made with the device as part of this higher level action taken by the agent, such as taps, text entries, waits, etc.
         */
        deviceInteractions?: Schema$GoogleFirebaseAppdistroV1alphaDeviceInteraction[];
    }
    /**
     * The results of running an automated test on a particular device.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaDeviceExecution {
        /**
         * Output only. Results of the AI steps if passed in
         */
        aiStepResults?: Schema$GoogleFirebaseAppdistroV1alphaAiStepResult[];
        /**
         * Output only. An app crash, if any occurred during the test.
         */
        appCrash?: Schema$GoogleFirebaseAppdistroV1alphaAppCrash;
        /**
         * Output only. A URI to an image of the Robo crawl graph.
         */
        crawlGraphUri?: string | null;
        /**
         * Required. The device that the test was run on.
         */
        device?: Schema$GoogleFirebaseAppdistroV1alphaTestDevice;
        /**
         * Output only. The reason why the test failed.
         */
        failedReason?: string | null;
        /**
         * Output only. The reason why the test was inconclusive.
         */
        inconclusiveReason?: string | null;
        /**
         * Output only. The path to a directory in Cloud Storage that will eventually contain the results for this execution. For example, gs://bucket/Nexus5-18-en-portrait.
         */
        resultsStoragePath?: string | null;
        /**
         * Output only. The statistics collected during the Robo test.
         */
        roboStats?: Schema$GoogleFirebaseAppdistroV1alphaRoboStats;
        /**
         * Output only. A list of screenshot image URIs taken from the Robo crawl. The file names are numbered by the order in which they were taken.
         */
        screenshotUris?: string[] | null;
        /**
         * Output only. The state of the test.
         */
        state?: string | null;
        /**
         * Output only. A URI to a video of the test run.
         */
        videoUri?: string | null;
    }
    /**
     * An interaction with the device, such as a tap, text entry, wait, etc.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaDeviceInteraction {
        /**
         * Output only. The screenshot used in the context of this action. The screen may have changed before the action was actually taken.
         */
        screenshot?: Schema$GoogleFirebaseAppdistroV1alphaScreenshot;
        /**
         * Output only. A swipe action.
         */
        swipe?: Schema$GoogleFirebaseAppdistroV1alphaDeviceInteractionSwipe;
        /**
         * Output only. A tap action.
         */
        tap?: Schema$AndroidxCrawlerOutputPoint;
        /**
         * Output only. Text entered for a text entry action.
         */
        textInput?: string | null;
        /**
         * Output only. A wait action.
         */
        wait?: Schema$GoogleFirebaseAppdistroV1alphaDeviceInteractionWait;
    }
    /**
     * A swipe action.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaDeviceInteractionSwipe {
        /**
         * Output only. The end point of the swipe.
         */
        end?: Schema$AndroidxCrawlerOutputPoint;
        /**
         * Output only. The start point of the swipe.
         */
        start?: Schema$AndroidxCrawlerOutputPoint;
    }
    /**
     * A wait action.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaDeviceInteractionWait {
        /**
         * Output only. The duration of the wait.
         */
        duration?: string | null;
    }
    export interface Schema$GoogleFirebaseAppdistroV1alphaEnableAccessOnReleaseRequest {
        /**
         * Optional. Ignored. Used to be build version of the app release if an instance identifier was provided for the release_id.
         */
        buildVersion?: string | null;
        /**
         * Optional. Ignored. Used to be display version of the app release if an instance identifier was provided for the release_id.
         */
        displayVersion?: string | null;
        /**
         * Optional. An email address which should get access to this release, <NAME_EMAIL>
         */
        emails?: string[] | null;
        /**
         * Optional. A repeated list of group aliases to enable access to a release for Note: This field is misnamed, but can't be changed because we need to maintain compatibility with old build tools
         */
        groupIds?: string[] | null;
    }
    export interface Schema$GoogleFirebaseAppdistroV1alphaEnableAccessOnReleaseResponse {
    }
    /**
     * Response object to get the release given a upload hash
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaGetReleaseByUploadHashResponse {
        /**
         * Release object
         */
        release?: Schema$GoogleFirebaseAppdistroV1alphaRelease;
    }
    /**
     * Response containing the UDIDs of tester iOS devices in a project
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse {
        /**
         * The UDIDs of tester iOS devices in a project
         */
        testerUdids?: Schema$GoogleFirebaseAppdistroV1alphaTesterUdid[];
    }
    export interface Schema$GoogleFirebaseAppdistroV1alphaGetUploadStatusResponse {
        /**
         * The error code associated with (only set on "FAILURE")
         */
        errorCode?: string | null;
        /**
         * Any additional context for the given upload status (e.g. error message) Meant to be displayed to the client
         */
        message?: string | null;
        /**
         * The release that was created from the upload (only set on "SUCCESS")
         */
        release?: Schema$GoogleFirebaseAppdistroV1alphaRelease;
        /**
         * The status of the upload
         */
        status?: string | null;
    }
    /**
     * An action taken by the AI agent while attempting to accomplish a goal.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaGoalAction {
        /**
         * Output only. A high level action taken by the AI on the device.
         */
        deviceAction?: Schema$GoogleFirebaseAppdistroV1alphaDeviceAction;
        /**
         * Output only. An explanation justifying why the action was taken.
         */
        explanation?: string | null;
        /**
         * Output only. An action taken by the AI to end the goal.
         */
        terminalAction?: Schema$GoogleFirebaseAppdistroV1alphaTerminalAction;
    }
    /**
     * Details for a goal step.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaGoalDetails {
        /**
         * Output only. The actions taken by the AI while attempting to accomplish the goal.
         */
        goalActions?: Schema$GoogleFirebaseAppdistroV1alphaGoalAction[];
    }
    export interface Schema$GoogleFirebaseAppdistroV1alphaJwt {
        token?: string | null;
    }
    /**
     * The response message for `ListReleaseTests`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaListReleaseTestsResponse {
        /**
         * A short-lived token, which can be sent as `pageToken` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The tests listed.
         */
        releaseTests?: Schema$GoogleFirebaseAppdistroV1alphaReleaseTest[];
    }
    /**
     * Login credential for automated tests
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaLoginCredential {
        /**
         * Optional. Hints to the crawler for identifying input fields
         */
        fieldHints?: Schema$GoogleFirebaseAppdistroV1alphaLoginCredentialFieldHints;
        /**
         * Optional. Are these credentials for Google?
         */
        google?: boolean | null;
        /**
         * Optional. Password for automated tests
         */
        password?: string | null;
        /**
         * Optional. Username for automated tests
         */
        username?: string | null;
    }
    /**
     * Hints to the crawler for identifying input fields
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaLoginCredentialFieldHints {
        /**
         * Required. The Android resource name of the password UI element. For example, in Java: R.string.foo in xml: @string/foo Only the "foo" part is needed. Reference doc: https://developer.android.com/guide/topics/resources/accessing-resources.html
         */
        passwordResourceName?: string | null;
        /**
         * Required. The Android resource name of the username UI element. For example, in Java: R.string.foo in xml: @string/foo Only the "foo" part is needed. Reference doc: https://developer.android.com/guide/topics/resources/accessing-resources.html
         */
        usernameResourceName?: string | null;
    }
    export interface Schema$GoogleFirebaseAppdistroV1alphaProvisionAppResponse {
    }
    /**
     * Proto defining a release object
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaRelease {
        /**
         * Release build version
         */
        buildVersion?: string | null;
        /**
         * Release version
         */
        displayVersion?: string | null;
        /**
         * Timestamp when the release was created
         */
        distributedAt?: string | null;
        /**
         * Release Id
         */
        id?: string | null;
        /**
         * Instance id of the release
         */
        instanceId?: string | null;
        /**
         * Last activity timestamp
         */
        lastActivityAt?: string | null;
        /**
         * Number of testers who have open invitations for the release
         */
        openInvitationCount?: number | null;
        /**
         * unused.
         */
        receivedAt?: string | null;
        /**
         * Release notes summary
         */
        releaseNotesSummary?: string | null;
        /**
         * Count of testers added to the release
         */
        testerCount?: number | null;
        /**
         * Number of testers who have installed the release
         */
        testerWithInstallCount?: number | null;
    }
    export interface Schema$GoogleFirebaseAppdistroV1alphaReleaseNotes {
        releaseNotes?: string | null;
    }
    /**
     * The results of running an automated test on a release.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaReleaseTest {
        /**
         * Optional. Instructions for AI driven test.
         */
        aiInstructions?: Schema$GoogleFirebaseAppdistroV1alphaAiInstructions;
        /**
         * Output only. Timestamp when the test was run.
         */
        createTime?: string | null;
        /**
         * Required. The results of the test on each device.
         */
        deviceExecutions?: Schema$GoogleFirebaseAppdistroV1alphaDeviceExecution[];
        /**
         * Optional. Input only. Login credentials for the test. Input only.
         */
        loginCredential?: Schema$GoogleFirebaseAppdistroV1alphaLoginCredential;
        /**
         * The name of the release test resource. Format: `projects/{project_number\}/apps/{app_id\}/releases/{release_id\}/tests/{test_id\}`
         */
        name?: string | null;
    }
    /**
     * Configuration for Robo crawler
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaRoboCrawler {
        /**
         * Optional. Instructions for AI driven test
         */
        aiInstructions?: Schema$GoogleFirebaseAppdistroV1alphaAiInstructions;
        /**
         * Optional. Login credential for automated tests
         */
        loginCredential?: Schema$GoogleFirebaseAppdistroV1alphaLoginCredential;
    }
    /**
     * Statistics collected during a Robo test.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaRoboStats {
        /**
         * Output only. Number of actions that crawler performed.
         */
        actionsPerformed?: number | null;
        /**
         * Output only. Duration of crawl.
         */
        crawlDuration?: string | null;
        /**
         * Output only. Number of distinct screens visited.
         */
        distinctVisitedScreens?: number | null;
        /**
         * Output only. Whether the main activity crawl timed out.
         */
        mainActivityCrawlTimedOut?: boolean | null;
    }
    /**
     * A device screenshot taken during a test.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaScreenshot {
        /**
         * Output only. The height of the screenshot, in pixels.
         */
        height?: number | null;
        /**
         * Output only. The URI of the screenshot.
         */
        uri?: string | null;
        /**
         * Output only. The width of the screenshot, in pixels.
         */
        width?: number | null;
    }
    /**
     * An action taken by the AI to end the goal.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaTerminalAction {
        /**
         * Output only. The reason why this goal was ended.
         */
        reason?: string | null;
        /**
         * Output only. The screenshot used in the context of this terminal action.
         */
        screenshot?: Schema$GoogleFirebaseAppdistroV1alphaScreenshot;
    }
    /**
     * Configuration for automated tests
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaTestConfig {
        /**
         * Identifier. The name of the test configuration resource. Format: `projects/{project_number\}/apps/{app_id\}/testConfig`
         */
        name?: string | null;
        /**
         * Optional. Configuration for Robo crawler
         */
        roboCrawler?: Schema$GoogleFirebaseAppdistroV1alphaRoboCrawler;
        /**
         * Optional. Tests will be run on this list of devices
         */
        testDevices?: Schema$GoogleFirebaseAppdistroV1alphaTestDevice[];
    }
    /**
     * A device on which automated tests can be run.
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaTestDevice {
        /**
         * Optional. The locale of the device (e.g. "en_US" for US English) during the test.
         */
        locale?: string | null;
        /**
         * Required. The device model.
         */
        model?: string | null;
        /**
         * Optional. The orientation of the device during the test.
         */
        orientation?: string | null;
        /**
         * Required. The version of the device (API level on Android).
         */
        version?: string | null;
    }
    /**
     * The UDIDs of a tester's iOS device
     */
    export interface Schema$GoogleFirebaseAppdistroV1alphaTesterUdid {
        /**
         * The name of the tester's device
         */
        name?: string | null;
        /**
         * The platform of the tester's device
         */
        platform?: string | null;
        /**
         * The UDID of the tester's device
         */
        udid?: string | null;
    }
    /**
     * A release of a Firebase app.
     */
    export interface Schema$GoogleFirebaseAppdistroV1Release {
        /**
         * Output only. A signed link (which expires in one hour) to directly download the app binary (IPA/APK/AAB) file.
         */
        binaryDownloadUri?: string | null;
        /**
         * Output only. Build version of the release. For an Android release, the build version is the `versionCode`. For an iOS release, the build version is the `CFBundleVersion`.
         */
        buildVersion?: string | null;
        /**
         * Output only. The time the release was created.
         */
        createTime?: string | null;
        /**
         * Output only. Display version of the release. For an Android release, the display version is the `versionName`. For an iOS release, the display version is the `CFBundleShortVersionString`.
         */
        displayVersion?: string | null;
        /**
         * Output only. A link to the Firebase console displaying a single release.
         */
        firebaseConsoleUri?: string | null;
        /**
         * The name of the release resource. Format: `projects/{project_number\}/apps/{app_id\}/releases/{release_id\}`
         */
        name?: string | null;
        /**
         * Notes of the release.
         */
        releaseNotes?: Schema$GoogleFirebaseAppdistroV1ReleaseNotes;
        /**
         * Output only. A link to the release in the tester web clip or Android app that lets testers (which were granted access to the app) view release notes and install the app onto their devices.
         */
        testingUri?: string | null;
    }
    /**
     * Notes that belong to a release.
     */
    export interface Schema$GoogleFirebaseAppdistroV1ReleaseNotes {
        /**
         * The text of the release notes.
         */
        text?: string | null;
    }
    /**
     * Operation metadata for `UploadRelease`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1UploadReleaseMetadata {
    }
    /**
     * Response message for `UploadRelease`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1UploadReleaseResponse {
        /**
         * Release associated with the uploaded binary.
         */
        release?: Schema$GoogleFirebaseAppdistroV1Release;
        /**
         * Result of upload release.
         */
        result?: string | null;
    }
    export class Resource$Apps {
        context: APIRequestContext;
        releases: Resource$Apps$Releases;
        release_by_hash: Resource$Apps$Release_by_hash;
        testers: Resource$Apps$Testers;
        upload_status: Resource$Apps$Upload_status;
        constructor(context: APIRequestContext);
        /**
         * Get the app, if it exists
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Apps$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaApp>;
        get(params: Params$Resource$Apps$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaApp>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaApp>): void;
        get(params: Params$Resource$Apps$Get, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaApp>): void;
        get(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaApp>): void;
        /**
         * Get a JWT token
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getJwt(params: Params$Resource$Apps$Getjwt, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getJwt(params?: Params$Resource$Apps$Getjwt, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaJwt>;
        getJwt(params: Params$Resource$Apps$Getjwt, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getJwt(params: Params$Resource$Apps$Getjwt, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaJwt>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaJwt>): void;
        getJwt(params: Params$Resource$Apps$Getjwt, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaJwt>): void;
        getJwt(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaJwt>): void;
        /**
         * Provision app distribution for an existing Firebase app, enabling it to subsequently be used by appdistro.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        provisionApp(params: Params$Resource$Apps$Provisionapp, options: StreamMethodOptions): GaxiosPromise<Readable>;
        provisionApp(params?: Params$Resource$Apps$Provisionapp, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaProvisionAppResponse>;
        provisionApp(params: Params$Resource$Apps$Provisionapp, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        provisionApp(params: Params$Resource$Apps$Provisionapp, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaProvisionAppResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaProvisionAppResponse>): void;
        provisionApp(params: Params$Resource$Apps$Provisionapp, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaProvisionAppResponse>): void;
        provisionApp(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaProvisionAppResponse>): void;
    }
    export interface Params$Resource$Apps$Get extends StandardParameters {
        /**
         * App view. When unset or set to BASIC, returns an App with everything set except for aab_state. When set to FULL, returns an App with aab_state set.
         */
        appView?: string;
        /**
         * Unique id for a Firebase app of the format: {version\}:{project_number\}:{platform\}:{hash(bundle_id)\} Example: 1:581234567376:android:aa0a3c7b135e90289
         */
        mobilesdkAppId?: string;
    }
    export interface Params$Resource$Apps$Getjwt extends StandardParameters {
        /**
         * Unique id for a Firebase app of the format: {version\}:{project_number\}:{platform\}:{hash(bundle_id)\} Example: 1:581234567376:android:aa0a3c7b135e90289
         */
        mobilesdkAppId?: string;
    }
    export interface Params$Resource$Apps$Provisionapp extends StandardParameters {
        /**
         * Unique id for a Firebase app of the format: {version\}:{project_number\}:{platform\}:{hash(bundle_id)\} Example: 1:581234567376:android:aa0a3c7b135e90289
         */
        mobilesdkAppId?: string;
    }
    export class Resource$Apps$Releases {
        context: APIRequestContext;
        notes: Resource$Apps$Releases$Notes;
        constructor(context: APIRequestContext);
        /**
         * Enable access on a release for testers.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        enable_access(params: Params$Resource$Apps$Releases$Enable_access, options: StreamMethodOptions): GaxiosPromise<Readable>;
        enable_access(params?: Params$Resource$Apps$Releases$Enable_access, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaEnableAccessOnReleaseResponse>;
        enable_access(params: Params$Resource$Apps$Releases$Enable_access, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        enable_access(params: Params$Resource$Apps$Releases$Enable_access, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaEnableAccessOnReleaseResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaEnableAccessOnReleaseResponse>): void;
        enable_access(params: Params$Resource$Apps$Releases$Enable_access, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaEnableAccessOnReleaseResponse>): void;
        enable_access(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaEnableAccessOnReleaseResponse>): void;
    }
    export interface Params$Resource$Apps$Releases$Enable_access extends StandardParameters {
        /**
         * Unique id for a Firebase app of the format: {version\}:{project_number\}:{platform\}:{hash(bundle_id)\} Example: 1:581234567376:android:aa0a3c7b135e90289
         */
        mobilesdkAppId?: string;
        /**
         * Release identifier
         */
        releaseId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1alphaEnableAccessOnReleaseRequest;
    }
    export class Resource$Apps$Releases$Notes {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Create release notes on a release.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Apps$Releases$Notes$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Apps$Releases$Notes$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaCreateReleaseNotesResponse>;
        create(params: Params$Resource$Apps$Releases$Notes$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Apps$Releases$Notes$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaCreateReleaseNotesResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaCreateReleaseNotesResponse>): void;
        create(params: Params$Resource$Apps$Releases$Notes$Create, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaCreateReleaseNotesResponse>): void;
        create(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaCreateReleaseNotesResponse>): void;
    }
    export interface Params$Resource$Apps$Releases$Notes$Create extends StandardParameters {
        /**
         * Unique id for a Firebase app of the format: {version\}:{project_number\}:{platform\}:{hash(bundle_id)\} Example: 1:581234567376:android:aa0a3c7b135e90289
         */
        mobilesdkAppId?: string;
        /**
         * Release identifier
         */
        releaseId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1alphaCreateReleaseNotesRequest;
    }
    export class Resource$Apps$Release_by_hash {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * GET Release by binary upload hash
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Release_by_hash$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Apps$Release_by_hash$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaGetReleaseByUploadHashResponse>;
        get(params: Params$Resource$Apps$Release_by_hash$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Release_by_hash$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetReleaseByUploadHashResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetReleaseByUploadHashResponse>): void;
        get(params: Params$Resource$Apps$Release_by_hash$Get, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetReleaseByUploadHashResponse>): void;
        get(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetReleaseByUploadHashResponse>): void;
    }
    export interface Params$Resource$Apps$Release_by_hash$Get extends StandardParameters {
        /**
         * Unique id for a Firebase app of the format: {version\}:{project_number\}:{platform\}:{hash(bundle_id)\} Example: 1:581234567376:android:aa0a3c7b135e90289
         */
        mobilesdkAppId?: string;
        /**
         * The hash for the upload
         */
        uploadHash?: string;
    }
    export class Resource$Apps$Testers {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Get UDIDs of tester iOS devices in a project
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getTesterUdids(params: Params$Resource$Apps$Testers$Gettesterudids, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getTesterUdids(params?: Params$Resource$Apps$Testers$Gettesterudids, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>;
        getTesterUdids(params: Params$Resource$Apps$Testers$Gettesterudids, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getTesterUdids(params: Params$Resource$Apps$Testers$Gettesterudids, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>): void;
        getTesterUdids(params: Params$Resource$Apps$Testers$Gettesterudids, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>): void;
        getTesterUdids(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>): void;
    }
    export interface Params$Resource$Apps$Testers$Gettesterudids extends StandardParameters {
        /**
         * Unique id for a Firebase app of the format: {version\}:{project_number\}:{platform\}:{hash(bundle_id)\} Example: 1:581234567376:android:aa0a3c7b135e90289
         */
        mobilesdkAppId?: string;
        /**
         * The name of the project, which is the parent of testers Format: `projects/{project_number\}`
         */
        project?: string;
    }
    export class Resource$Apps$Upload_status {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * GET Binary upload status by token
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Upload_status$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Apps$Upload_status$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaGetUploadStatusResponse>;
        get(params: Params$Resource$Apps$Upload_status$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Upload_status$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetUploadStatusResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetUploadStatusResponse>): void;
        get(params: Params$Resource$Apps$Upload_status$Get, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetUploadStatusResponse>): void;
        get(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetUploadStatusResponse>): void;
    }
    export interface Params$Resource$Apps$Upload_status$Get extends StandardParameters {
        /**
         * Unique id for a Firebase app of the format: {version\}:{project_number\}:{platform\}:{hash(bundle_id)\} Example: 1:581234567376:android:aa0a3c7b135e90289
         */
        mobilesdkAppId?: string;
        /**
         * The token for the upload
         */
        uploadToken?: string;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        apps: Resource$Projects$Apps;
        testers: Resource$Projects$Testers;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Apps {
        context: APIRequestContext;
        releases: Resource$Projects$Apps$Releases;
        constructor(context: APIRequestContext);
        /**
         * Gets configuration for automated tests.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getTestConfig(params: Params$Resource$Projects$Apps$Gettestconfig, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getTestConfig(params?: Params$Resource$Projects$Apps$Gettestconfig, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>;
        getTestConfig(params: Params$Resource$Projects$Apps$Gettestconfig, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getTestConfig(params: Params$Resource$Projects$Apps$Gettestconfig, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>): void;
        getTestConfig(params: Params$Resource$Projects$Apps$Gettestconfig, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>): void;
        getTestConfig(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>): void;
        /**
         * Updates a release.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        updateTestConfig(params: Params$Resource$Projects$Apps$Updatetestconfig, options: StreamMethodOptions): GaxiosPromise<Readable>;
        updateTestConfig(params?: Params$Resource$Projects$Apps$Updatetestconfig, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>;
        updateTestConfig(params: Params$Resource$Projects$Apps$Updatetestconfig, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        updateTestConfig(params: Params$Resource$Projects$Apps$Updatetestconfig, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>): void;
        updateTestConfig(params: Params$Resource$Projects$Apps$Updatetestconfig, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>): void;
        updateTestConfig(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaTestConfig>): void;
    }
    export interface Params$Resource$Projects$Apps$Gettestconfig extends StandardParameters {
        /**
         * Required. The name of the `TestConfig` resource to retrieve. Format: `projects/{project_number\}/apps/{app_id\}/testConfig`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Apps$Updatetestconfig extends StandardParameters {
        /**
         * Identifier. The name of the test configuration resource. Format: `projects/{project_number\}/apps/{app_id\}/testConfig`
         */
        name?: string;
        /**
         * Optional. The list of fields to update.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1alphaTestConfig;
    }
    export class Resource$Projects$Apps$Releases {
        context: APIRequestContext;
        tests: Resource$Projects$Apps$Releases$Tests;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Apps$Releases$Tests {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Run automated test(s) on release.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Apps$Releases$Tests$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Apps$Releases$Tests$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>;
        create(params: Params$Resource$Projects$Apps$Releases$Tests$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Apps$Releases$Tests$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>): void;
        create(params: Params$Resource$Projects$Apps$Releases$Tests$Create, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>): void;
        create(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>): void;
        /**
         * Get results for automated test run on release.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Apps$Releases$Tests$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Apps$Releases$Tests$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>;
        get(params: Params$Resource$Projects$Apps$Releases$Tests$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Apps$Releases$Tests$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>): void;
        get(params: Params$Resource$Projects$Apps$Releases$Tests$Get, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>): void;
        get(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaReleaseTest>): void;
        /**
         * List results for automated tests run on release.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Apps$Releases$Tests$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Apps$Releases$Tests$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaListReleaseTestsResponse>;
        list(params: Params$Resource$Projects$Apps$Releases$Tests$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Apps$Releases$Tests$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaListReleaseTestsResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaListReleaseTestsResponse>): void;
        list(params: Params$Resource$Projects$Apps$Releases$Tests$List, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaListReleaseTestsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaListReleaseTestsResponse>): void;
    }
    export interface Params$Resource$Projects$Apps$Releases$Tests$Create extends StandardParameters {
        /**
         * Required. The name of the release resource, which is the parent of the test Format: `projects/{project_number\}/apps/{app_id\}/releases/{release_id\}`
         */
        parent?: string;
        /**
         * Optional. The ID to use for the test, which will become the final component of the tests's resource name. This value should be 4-63 characters, and valid characters are /a-z-/. If it is not provided one will be automatically generated.
         */
        releaseTestId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1alphaReleaseTest;
    }
    export interface Params$Resource$Projects$Apps$Releases$Tests$Get extends StandardParameters {
        /**
         * Required. The name of the release test resource. Format: `projects/{project_number\}/apps/{app_id\}/releases/{release_id\}/tests/{test_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Apps$Releases$Tests$List extends StandardParameters {
        /**
         * Optional. The maximum number of tests to return. The service may return fewer than this value.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListReleaseTests` call. Provide this to retrieve the subsequent page.
         */
        pageToken?: string;
        /**
         * Required. The name of the release resource, which is the parent of the tests Format: `projects/{project_number\}/apps/{app_id\}/releases/{release_id\}`
         */
        parent?: string;
    }
    export class Resource$Projects$Testers {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Get UDIDs of tester iOS devices in a project
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getUdids(params: Params$Resource$Projects$Testers$Getudids, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getUdids(params?: Params$Resource$Projects$Testers$Getudids, options?: MethodOptions): GaxiosPromise<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>;
        getUdids(params: Params$Resource$Projects$Testers$Getudids, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getUdids(params: Params$Resource$Projects$Testers$Getudids, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>): void;
        getUdids(params: Params$Resource$Projects$Testers$Getudids, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>): void;
        getUdids(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1alphaGetTesterUdidsResponse>): void;
    }
    export interface Params$Resource$Projects$Testers$Getudids extends StandardParameters {
        /**
         * Unique id for a Firebase app of the format: {version\}:{project_number\}:{platform\}:{hash(bundle_id)\} Example: 1:581234567376:android:aa0a3c7b135e90289
         */
        mobilesdkAppId?: string;
        /**
         * The name of the project, which is the parent of testers Format: `projects/{project_number\}`
         */
        project?: string;
    }
    export {};
}
