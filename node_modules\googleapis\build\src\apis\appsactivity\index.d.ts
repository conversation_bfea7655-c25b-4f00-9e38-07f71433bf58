/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { appsactivity_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof appsactivity_v1.Appsactivity;
};
export declare function appsactivity(version: 'v1'): appsactivity_v1.Appsactivity;
export declare function appsactivity(options: appsactivity_v1.Options): appsactivity_v1.Appsactivity;
declare const auth: AuthPlus;
export { auth };
export { appsactivity_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
