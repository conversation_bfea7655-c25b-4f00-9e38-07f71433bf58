/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { gkeonprem_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof gkeonprem_v1.Gkeonprem;
};
export declare function gkeonprem(version: 'v1'): gkeonprem_v1.Gkeonprem;
export declare function gkeonprem(options: gkeonprem_v1.Options): gkeonprem_v1.Gkeonprem;
declare const auth: AuthPlus;
export { auth };
export { gkeonprem_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
