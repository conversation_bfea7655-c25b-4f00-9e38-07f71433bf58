/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { developerconnect_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof developerconnect_v1.Developerconnect;
};
export declare function developerconnect(version: 'v1'): developerconnect_v1.Developerconnect;
export declare function developerconnect(options: developerconnect_v1.Options): developerconnect_v1.Developerconnect;
declare const auth: AuthPlus;
export { auth };
export { developerconnect_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
