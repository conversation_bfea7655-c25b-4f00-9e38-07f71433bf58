/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { walletobjects_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof walletobjects_v1.Walletobjects;
};
export declare function walletobjects(version: 'v1'): walletobjects_v1.Walletobjects;
export declare function walletobjects(options: walletobjects_v1.Options): walletobjects_v1.Walletobjects;
declare const auth: AuthPlus;
export { auth };
export { walletobjects_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
