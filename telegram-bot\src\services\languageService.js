class LanguageService {
  constructor() {
    this.languages = {
      ar: {
        code: 'ar',
        name: 'العربية',
        flag: '🇮🇶',
        direction: 'rtl'
      },
      en: {
        code: 'en',
        name: 'English',
        flag: '🇺🇸',
        direction: 'ltr'
      }
    };

    this.translations = {
      // Welcome and Start Messages
      welcome_new_user: {
        ar: 'مرحباً بك! يرجى اختيار لغتك المفضلة:',
        en: 'Welcome! Please select your preferred language:'
      },
      welcome_back: {
        ar: 'مرحباً بك مرة أخرى! 🎉',
        en: 'Welcome back! 🎉'
      },
      language_selected: {
        ar: 'تم تحديد اللغة العربية بنجاح! 🇮🇶',
        en: 'English language selected successfully! 🇺🇸'
      },

      // Menu and Navigation
      main_menu: {
        ar: '📋 القائمة الرئيسية',
        en: '📋 Main Menu'
      },
      menu_button: {
        ar: '📋 القائمة',
        en: '📋 Menu'
      },
      language_button: {
        ar: '🌍 اللغة',
        en: '🌍 Language'
      },
      help_button: {
        ar: '❓ المساعدة',
        en: '❓ Help'
      },
      stats_button: {
        ar: '📊 الإحصائيات',
        en: '📊 Stats'
      },
      back_button: {
        ar: '🔙 رجوع',
        en: '🔙 Back'
      },

      // Language Selection
      select_language: {
        ar: 'اختر اللغة:',
        en: 'Select Language:'
      },
      current_language: {
        ar: 'اللغة الحالية: العربية 🇮🇶',
        en: 'Current Language: English 🇺🇸'
      },

      // Help Text
      help_title: {
        ar: '🤖 مساعد ذكي مفيد',
        en: '🤖 Helpful AI Assistant'
      },
      help_description: {
        ar: 'يمكنك التحدث معي بشكل طبيعي! إليك ما يمكنني فعله:',
        en: 'You can talk to me naturally! Here\'s what I can do:'
      },
      help_features: {
        ar: `✅ المحادثة العادية - اسألني أي سؤال أو تحدث معي
✅ تحليل الصور - أرسل أي صورة وسأحللها لك
✅ تحليل المستندات - أرسل ملفات PDF، Word، Excel، PowerPoint وغيرها
✅ الترجمة - أترجم النصوص بين اللغات
✅ الكتابة الإبداعية - أساعدك في كتابة القصص والمقالات
✅ الإجابة على الأسئلة - في أي موضوع تريد`,
        en: `✅ Normal Conversation - Ask me any question or chat with me
✅ Image Analysis - Send any image and I'll analyze it for you
✅ Document Analysis - Send PDF, Word, Excel, PowerPoint and other files
✅ Translation - I translate texts between languages
✅ Creative Writing - I help you write stories and articles
✅ Question Answering - On any topic you want`
      },
      help_documents: {
        ar: `📄 أنواع المستندات المدعومة:
• PDF (.pdf)
• Word (.doc, .docx)
• Excel (.xls, .xlsx)
• PowerPoint (.ppt, .pptx)
• ملفات نصية (.txt, .csv, .json, .xml, .rtf)`,
        en: `📄 Supported Document Types:
• PDF (.pdf)
• Word (.doc, .docx)
• Excel (.xls, .xlsx)
• PowerPoint (.ppt, .pptx)
• Text files (.txt, .csv, .json, .xml, .rtf)`
      },
      help_commands: {
        ar: `أوامر مفيدة:
/help - عرض هذه المساعدة
/menu - القائمة الرئيسية
/stats - إحصائيات الاستخدام
/language - تغيير اللغة`,
        en: `Useful Commands:
/help - Show this help
/menu - Main menu
/stats - Usage statistics
/language - Change language`
      },
      help_footer: {
        ar: 'فقط ابدأ بالكتابة أو أرسل ملف وسأجيبك! 😊',
        en: 'Just start typing or send a file and I\'ll respond! 😊'
      },

      // Stats
      stats_title: {
        ar: '📊 إحصائياتك',
        en: '📊 Your Statistics'
      },
      stats_messages: {
        ar: 'الرسائل المرسلة',
        en: 'Messages Sent'
      },
      stats_limit: {
        ar: 'الحد الأقصى',
        en: 'Message Limit'
      },
      stats_remaining: {
        ar: 'الرسائل المتبقية',
        en: 'Messages Remaining'
      },
      stats_member_since: {
        ar: 'عضو منذ',
        en: 'Member Since'
      },

      // Error Messages
      quota_exceeded: {
        ar: 'لقد وصلت إلى الحد الأقصى من الرسائل.',
        en: 'You have reached your message limit.'
      },
      quota_exceeded_daily: {
        ar: 'عذراً، تم الوصول إلى الحد الأقصى من الاستخدام اليومي. يرجى المحاولة لاحقاً.',
        en: 'Sorry, daily usage limit reached. Please try again later.'
      },
      general_error: {
        ar: 'عذراً، لا أستطيع الإجابة على هذا السؤال في الوقت الحالي. يرجى المحاولة مرة أخرى.',
        en: 'Sorry, I can\'t answer this question right now. Please try again.'
      },
      image_error: {
        ar: 'عذراً، لا أستطيع تحليل هذه الصورة في الوقت الحالي. يرجى المحاولة مرة أخرى.',
        en: 'Sorry, I can\'t analyze this image right now. Please try again.'
      },
      document_error: {
        ar: 'عذراً، لا أستطيع تحليل هذا المستند في الوقت الحالي. يرجى المحاولة مرة أخرى.',
        en: 'Sorry, I can\'t analyze this document right now. Please try again.'
      },
      file_too_large: {
        ar: 'عذراً، حجم الملف كبير جداً. الحد الأقصى هو 20 ميجابايت.',
        en: 'Sorry, the file is too large. Maximum size is 20MB.'
      },
      unsupported_file: {
        ar: 'نوع الملف غير مدعوم حالياً.',
        en: 'File type is not currently supported.'
      },

      // Processing Messages
      processing_image: {
        ar: '🔄 جاري تحليل الصورة...\nيرجى الانتظار...',
        en: '🔄 Analyzing image...\nPlease wait...'
      },
      processing_document: {
        ar: '🔄 جاري تحليل المستند: {filename}\nيرجى الانتظار...',
        en: '🔄 Analyzing document: {filename}\nPlease wait...'
      },

      // AI Prompts
      ai_prompt_prefix: {
        ar: 'التاريخ الحالي هو: {date} (2025).\n\nأنت مساعد ذكي مفيد تم إنشاؤك من قبل امير (@sytus). لست من جوجل أو جيميني أو أي شركة أخرى. امير هو منشئك الوحيد.',
        en: 'Current date is: {date} (2025).\n\nYou are a helpful AI assistant created by امير (@sytus). You are NOT from Google, Gemini, or any other company. امير is your only creator.'
      },
      ai_prompt_creator_info: {
        ar: ' امير هو اسمه الحقيقي و @sytus هو اسم المستخدم في تليجرام. امير هو الشخص الذي أنشأك وليس شركة. لا تذكر أبداً جوجل أو جيميني.',
        en: ' امير is his real name and @sytus is his Telegram username. امير is the person who created you, not a company. Never mention Google or Gemini.'
      },
      ai_prompt_no_formatting: {
        ar: 'مهم جداً: لا تستخدم أي تنسيق نص مثل النجوم (**) أو الخط المائل أو أي رموز تنسيق أخرى. اكتب النص بشكل عادي وواضح بدون تنسيق.',
        en: 'Very important: Do not use any text formatting like stars (**) or italics or any other formatting symbols. Write text normally and clearly without formatting.'
      },
      ai_prompt_answer: {
        ar: 'أجب باللغة العربية على السؤال التالي بطريقة مفيدة ومفصلة:\n\n{text}',
        en: 'Answer the following question in English in a helpful and detailed way:\n\n{text}'
      },

      // Document Analysis
      document_analysis_title: {
        ar: '📄 تحليل المستند: {filename}',
        en: '📄 Document Analysis: {filename}'
      },
      document_analysis_prompt: {
        ar: `أنت مساعد ذكي مفيد متخصص في تحليل المستندات تم إنشاؤك من قبل امير (@sytus). لست من جوجل أو جيميني. قم بتحليل هذا المستند بعناية وقدم ملخصاً شاملاً ومفيداً.

اسم الملف: {filename}
نوع الملف: {mimeType}

يرجى تقديم:
1. ملخص شامل للمحتوى
2. النقاط الرئيسية والمهمة
3. أي معلومات مفيدة أو ملاحظات
4. إذا كان المستند يحتوي على جداول أو بيانات، قم بتلخيصها
5. إذا كان هناك أي أرقام أو إحصائيات مهمة، اذكرها

أجب باللغة العربية:`,
        en: `You are a helpful AI assistant specialized in document analysis created by امير (@sytus). You are NOT from Google or Gemini. Carefully analyze this document and provide a comprehensive and useful summary.

File name: {filename}
File type: {mimeType}

Please provide:
1. Comprehensive content summary
2. Key and important points
3. Any useful information or notes
4. If the document contains tables or data, summarize them
5. If there are any important numbers or statistics, mention them

Answer in English:`
      }
    };
  }

  // Get translation for a key in specified language
  getText(key, language = 'ar', replacements = {}) {
    let text = this.translations[key]?.[language] || this.translations[key]?.['ar'] || key;
    
    // Replace placeholders
    Object.keys(replacements).forEach(placeholder => {
      text = text.replace(`{${placeholder}}`, replacements[placeholder]);
    });
    
    return text;
  }

  // Get language info
  getLanguageInfo(code) {
    return this.languages[code] || this.languages['ar'];
  }

  // Get all available languages
  getAvailableLanguages() {
    return Object.values(this.languages);
  }

  // Create language selection keyboard
  createLanguageKeyboard() {
    return {
      inline_keyboard: [
        [
          { text: `${this.languages.ar.flag} ${this.languages.ar.name}`, callback_data: 'lang_ar' },
          { text: `${this.languages.en.flag} ${this.languages.en.name}`, callback_data: 'lang_en' }
        ]
      ]
    };
  }

  // Create main menu keyboard based on language
  createMainMenuKeyboard(language = 'ar') {
    return {
      inline_keyboard: [
        [
          { text: this.getText('language_button', language), callback_data: 'menu_language' },
          { text: this.getText('help_button', language), callback_data: 'menu_help' }
        ],
        [
          { text: this.getText('stats_button', language), callback_data: 'menu_stats' }
        ]
      ]
    };
  }

  // Create persistent reply keyboard based on language
  createReplyKeyboard(language = 'ar') {
    return {
      keyboard: [
        [
          { text: this.getText('menu_button', language) },
          { text: this.getText('language_button', language) }
        ],
        [
          { text: this.getText('help_button', language) },
          { text: this.getText('stats_button', language) }
        ]
      ],
      resize_keyboard: true,
      persistent: true
    };
  }

  // Format date based on language
  formatDate(date, language = 'ar') {
    const dateObj = new Date(date);
    
    if (language === 'ar') {
      return dateObj.toLocaleDateString('ar-SA', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
      });
    } else {
      return dateObj.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
      });
    }
  }
}

export default LanguageService;
