/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { advisorynotifications_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof advisorynotifications_v1.Advisorynotifications;
};
export declare function advisorynotifications(version: 'v1'): advisorynotifications_v1.Advisorynotifications;
export declare function advisorynotifications(options: advisorynotifications_v1.Options): advisorynotifications_v1.Advisorynotifications;
declare const auth: AuthPlus;
export { auth };
export { advisorynotifications_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
