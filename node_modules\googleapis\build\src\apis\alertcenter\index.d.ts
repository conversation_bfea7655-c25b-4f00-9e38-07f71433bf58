/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { alertcenter_v1beta1 } from './v1beta1';
export declare const VERSIONS: {
    v1beta1: typeof alertcenter_v1beta1.Alertcenter;
};
export declare function alertcenter(version: 'v1beta1'): alertcenter_v1beta1.Alertcenter;
export declare function alertcenter(options: alertcenter_v1beta1.Options): alertcenter_v1beta1.Alertcenter;
declare const auth: AuthPlus;
export { auth };
export { alertcenter_v1beta1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
