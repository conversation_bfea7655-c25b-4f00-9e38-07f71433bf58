/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace file_v1beta1 {
    export interface Options extends GlobalOptions {
        version: 'v1beta1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Cloud Filestore API
     *
     * The Cloud Filestore API is used for creating and managing cloud file servers.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const file = google.file('v1beta1');
     * ```
     */
    export class File {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * A Filestore backup.
     */
    export interface Schema$Backup {
        /**
         * Output only. Capacity of the source file share when the backup was created.
         */
        capacityGb?: string | null;
        /**
         * Output only. The time when the backup was created.
         */
        createTime?: string | null;
        /**
         * A description of the backup with 2048 characters or less. Requests with longer descriptions will be rejected.
         */
        description?: string | null;
        /**
         * Output only. Amount of bytes that will be downloaded if the backup is restored
         */
        downloadBytes?: string | null;
        /**
         * Output only. The file system protocol of the source Filestore instance that this backup is created from.
         */
        fileSystemProtocol?: string | null;
        /**
         * Immutable. KMS key name used for data encryption.
         */
        kmsKeyName?: string | null;
        /**
         * Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The resource name of the backup, in the format `projects/{project_id\}/locations/{location_id\}/backups/{backup_id\}`.
         */
        name?: string | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzi?: boolean | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Name of the file share in the source Filestore instance that the backup is created from.
         */
        sourceFileShare?: string | null;
        /**
         * The resource name of the source Filestore instance, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}`, used to create this backup.
         */
        sourceInstance?: string | null;
        /**
         * Output only. The service tier of the source Filestore instance that this backup is created from.
         */
        sourceInstanceTier?: string | null;
        /**
         * Output only. The backup state.
         */
        state?: string | null;
        /**
         * Output only. The size of the storage used by the backup. As backups share storage, this number is expected to change with backup creation/deletion.
         */
        storageBytes?: string | null;
        /**
         * Optional. Input only. Immutable. Tag keys/values directly bound to this resource. For example: "123/environment": "production", "123/costCenter": "marketing"
         */
        tags?: {
            [key: string]: string;
        } | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Time window specified for daily operations.
     */
    export interface Schema$DailyCycle {
        /**
         * Output only. Duration of the time window, set by service producer.
         */
        duration?: string | null;
        /**
         * Time within the day to start the operations.
         */
        startTime?: Schema$TimeOfDay;
    }
    /**
     * Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp
     */
    export interface Schema$Date {
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        day?: number | null;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        month?: number | null;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        year?: number | null;
    }
    /**
     * DenyMaintenancePeriod definition. Maintenance is forbidden within the deny period. The start_date must be less than the end_date.
     */
    export interface Schema$DenyMaintenancePeriod {
        /**
         * Deny period end date. This can be: * A full date, with non-zero year, month and day values. * A month and day value, with a zero year. Allows recurring deny periods each year. Date matching this period will have to be before the end.
         */
        endDate?: Schema$Date;
        /**
         * Deny period start date. This can be: * A full date, with non-zero year, month and day values. * A month and day value, with a zero year. Allows recurring deny periods each year. Date matching this period will have to be the same or after the start.
         */
        startDate?: Schema$Date;
        /**
         * Time in UTC when the Blackout period starts on start_date and ends on end_date. This can be: * Full time. * All zeros for 00:00:00 UTC
         */
        time?: Schema$TimeOfDay;
    }
    /**
     * Directory Services configuration for Kerberos-based authentication.
     */
    export interface Schema$DirectoryServicesConfig {
        /**
         * Configuration for Managed Service for Microsoft Active Directory.
         */
        managedActiveDirectory?: Schema$ManagedActiveDirectoryConfig;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * File share configuration for the instance.
     */
    export interface Schema$FileShareConfig {
        /**
         * File share capacity in gigabytes (GB). Filestore defines 1 GB as 1024^3 bytes.
         */
        capacityGb?: string | null;
        /**
         * Required. The name of the file share. Must use 1-16 characters for the basic service tier and 1-63 characters for all other service tiers. Must use lowercase letters, numbers, or underscores `[a-z0-9_]`. Must start with a letter. Immutable.
         */
        name?: string | null;
        /**
         * Nfs Export Options. There is a limit of 10 export options per file share.
         */
        nfsExportOptions?: Schema$NfsExportOptions[];
        /**
         * The resource name of the backup, in the format `projects/{project_id\}/locations/{location_id\}/backups/{backup_id\}`, that this file share has been restored from.
         */
        sourceBackup?: string | null;
    }
    /**
     * Instance represents the interface for SLM services to actuate the state of control plane resources. Example Instance in JSON, where consumer-project-number=123456, producer-project-id=cloud-sql: ```json Instance: { "name": "projects/123456/locations/us-east1/instances/prod-instance", "create_time": { "seconds": **********, \}, "labels": { "env": "prod", "foo": "bar" \}, "state": READY, "software_versions": { "software_update": "cloud-sql-09-28-2018", \}, "maintenance_policy_names": { "UpdatePolicy": "projects/123456/locations/us-east1/maintenancePolicies/prod-update-policy", \} "tenant_project_id": "cloud-sql-test-tenant", "producer_metadata": { "cloud-sql-tier": "basic", "cloud-sql-instance-size": "1G", \}, "provisioned_resources": [ { "resource-type": "compute-instance", "resource-url": "https://www.googleapis.com/compute/v1/projects/cloud-sql/zones/us-east1-b/instances/vm-1", \} ], "maintenance_schedules": { "csa_rollout": { "start_time": { "seconds": **********, \}, "end_time": { "seconds": **********, \}, \}, "ncsa_rollout": { "start_time": { "seconds": **********, \}, "end_time": { "seconds": **********, \}, \} \}, "consumer_defined_name": "my-sql-instance1", \} ``` LINT.IfChange
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1Instance {
        /**
         * consumer_defined_name is the name of the instance set by the service consumers. Generally this is different from the `name` field which reperesents the system-assigned id of the instance which the service consumers do not recognize. This is a required field for tenants onboarding to Maintenance Window notifications (go/slm-rollout-maintenance-policies#prerequisites).
         */
        consumerDefinedName?: string | null;
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. The instance_type of this instance of format: projects/{project_number\}/locations/{location_id\}/instanceTypes/{instance_type_id\}. Instance Type represents a high-level tier or SKU of the service that this instance belong to. When enabled(eg: Maintenance Rollout), Rollout uses 'instance_type' along with 'software_versions' to determine whether instance needs an update or not.
         */
        instanceType?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata. Each label is a key-value pair, where both the key and the value are arbitrary strings provided by the user.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. The MaintenancePolicies that have been attached to the instance. The key must be of the type name of the oneof policy name defined in MaintenancePolicy, and the referenced policy must define the same policy type. For details, please refer to go/mr-user-guide. Should not be set if maintenance_settings.maintenance_policies is set.
         */
        maintenancePolicyNames?: {
            [key: string]: string;
        } | null;
        /**
         * The MaintenanceSchedule contains the scheduling information of published maintenance schedule with same key as software_versions.
         */
        maintenanceSchedules?: {
            [key: string]: Schema$GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule;
        } | null;
        /**
         * Optional. The MaintenanceSettings associated with instance.
         */
        maintenanceSettings?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings;
        /**
         * Unique name of the resource. It uses the form: `projects/{project_number\}/locations/{location_id\}/instances/{instance_id\}` Note: This name is passed, stored and logged across the rollout system. So use of consumer project_id or any other consumer PII in the name is strongly discouraged for wipeout (go/wipeout) compliance. See go/elysium/project_ids#storage-guidance for more details.
         */
        name?: string | null;
        /**
         * Optional. notification_parameter are information that service producers may like to include that is not relevant to Rollout. This parameter will only be passed to Gamma and Cloud Logging for notification/logging purpose.
         */
        notificationParameters?: {
            [key: string]: Schema$GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter;
        } | null;
        /**
         * Output only. Custom string attributes used primarily to expose producer-specific information in monitoring dashboards. See go/get-instance-metadata.
         */
        producerMetadata?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The list of data plane resources provisioned for this instance, e.g. compute VMs. See go/get-instance-metadata.
         */
        provisionedResources?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource[];
        /**
         * Link to the SLM instance template. Only populated when updating SLM instances via SSA's Actuation service adaptor. Service producers with custom control plane (e.g. Cloud SQL) doesn't need to populate this field. Instead they should use software_versions.
         */
        slmInstanceTemplate?: string | null;
        /**
         * Output only. SLO metadata for instance classification in the Standardized dataplane SLO platform. See go/cloud-ssa-standard-slo for feature description.
         */
        sloMetadata?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata;
        /**
         * Software versions that are used to deploy this instance. This can be mutated by rollout services.
         */
        softwareVersions?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Current lifecycle state of the resource (e.g. if it's being created or ready to use).
         */
        state?: string | null;
        /**
         * Output only. ID of the associated GCP tenant project. See go/get-instance-metadata.
         */
        tenantProjectId?: string | null;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
    }
    /**
     * Maintenance schedule which is exposed to customer and potentially end user, indicating published upcoming future maintenance schedule
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule {
        /**
         * This field is deprecated, and will be always set to true since reschedule can happen multiple times now. This field should not be removed until all service producers remove this for their customers.
         */
        canReschedule?: boolean | null;
        /**
         * The scheduled end time for the maintenance.
         */
        endTime?: string | null;
        /**
         * The rollout management policy this maintenance schedule is associated with. When doing reschedule update request, the reschedule should be against this given policy.
         */
        rolloutManagementPolicy?: string | null;
        /**
         * schedule_deadline_time is the time deadline any schedule start time cannot go beyond, including reschedule. It's normally the initial schedule start time plus maintenance window length (1 day or 1 week). Maintenance cannot be scheduled to start beyond this deadline.
         */
        scheduleDeadlineTime?: string | null;
        /**
         * The scheduled start time for the maintenance.
         */
        startTime?: string | null;
    }
    /**
     * Maintenance settings associated with instance. Allows service producers and end users to assign settings that controls maintenance on this instance.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings {
        /**
         * Optional. Exclude instance from maintenance. When true, rollout service will not attempt maintenance on the instance. Rollout service will include the instance in reported rollout progress as not attempted.
         */
        exclude?: boolean | null;
        /**
         * Optional. If the update call is triggered from rollback, set the value as true.
         */
        isRollback?: boolean | null;
        /**
         * Optional. The MaintenancePolicies that have been attached to the instance. The key must be of the type name of the oneof policy name defined in MaintenancePolicy, and the embedded policy must define the same policy type. For details, please refer to go/mr-user-guide. Should not be set if maintenance_policy_names is set. If only the name is needed, then only populate MaintenancePolicy.name.
         */
        maintenancePolicies?: {
            [key: string]: Schema$MaintenancePolicy;
        } | null;
    }
    /**
     * Node information for custom per-node SLO implementations. SSA does not support per-node SLO, but producers can populate per-node information in SloMetadata for custom precomputations. SSA Eligibility Exporter will emit per-node metric based on this information.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata {
        /**
         * The location of the node, if different from instance location.
         */
        location?: string | null;
        /**
         * The id of the node. This should be equal to SaasInstanceNode.node_id.
         */
        nodeId?: string | null;
        /**
         * If present, this will override eligibility for the node coming from instance or exclusions for specified SLIs.
         */
        perSliEligibility?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility;
    }
    /**
     * Contains notification related data.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter {
        /**
         * Optional. Array of string values. e.g. instance's replica information.
         */
        values?: string[] | null;
    }
    /**
     * PerSliSloEligibility is a mapping from an SLI name to eligibility.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility {
        /**
         * An entry in the eligibilities map specifies an eligibility for a particular SLI for the given instance. The SLI key in the name must be a valid SLI name specified in the Eligibility Exporter binary flags otherwise an error will be emitted by Eligibility Exporter and the oncaller will be alerted. If an SLI has been defined in the binary flags but the eligibilities map does not contain it, the corresponding SLI time series will not be emitted by the Eligibility Exporter. This ensures a smooth rollout and compatibility between the data produced by different versions of the Eligibility Exporters. If eligibilities map contains a key for an SLI which has not been declared in the binary flags, there will be an error message emitted in the Eligibility Exporter log and the metric for the SLI in question will not be emitted.
         */
        eligibilities?: {
            [key: string]: Schema$GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility;
        } | null;
    }
    /**
     * Describes provisioned dataplane resources.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource {
        /**
         * Type of the resource. This can be either a GCP resource or a custom one (e.g. another cloud provider's VM). For GCP compute resources use singular form of the names listed in GCP compute API documentation (https://cloud.google.com/compute/docs/reference/rest/v1/), prefixed with 'compute-', for example: 'compute-instance', 'compute-disk', 'compute-autoscaler'.
         */
        resourceType?: string | null;
        /**
         * URL identifying the resource, e.g. "https://www.googleapis.com/compute/v1/projects/...)".
         */
        resourceUrl?: string | null;
    }
    /**
     * SloEligibility is a tuple containing eligibility value: true if an instance is eligible for SLO calculation or false if it should be excluded from all SLO-related calculations along with a user-defined reason.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility {
        /**
         * Whether an instance is eligible or ineligible.
         */
        eligible?: boolean | null;
        /**
         * User-defined reason for the current value of instance eligibility. Usually, this can be directly mapped to the internal state. An empty reason is allowed.
         */
        reason?: string | null;
    }
    /**
     * SloMetadata contains resources required for proper SLO classification of the instance.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata {
        /**
         * Optional. List of nodes. Some producers need to use per-node metadata to calculate SLO. This field allows such producers to publish per-node SLO meta data, which will be consumed by SSA Eligibility Exporter and published in the form of per node metric to Monarch.
         */
        nodes?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata[];
        /**
         * Optional. Multiple per-instance SLI eligibilities which apply for individual SLIs.
         */
        perSliEligibility?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility;
        /**
         * Name of the SLO tier the Instance belongs to. This name will be expected to match the tiers specified in the service SLO configuration. Field is mandatory and must not be empty.
         */
        tier?: string | null;
    }
    /**
     * A Filestore instance.
     */
    export interface Schema$Instance {
        /**
         * The storage capacity of the instance in gigabytes (GB = 1024^3 bytes). This capacity can be increased up to `max_capacity_gb` GB in multipliers of `capacity_step_size_gb` GB.
         */
        capacityGb?: string | null;
        /**
         * Output only. The increase/decrease capacity step size.
         */
        capacityStepSizeGb?: string | null;
        /**
         * Output only. The time when the instance was created.
         */
        createTime?: string | null;
        /**
         * The description of the instance (2048 characters or less).
         */
        description?: string | null;
        /**
         * Optional. Directory Services configuration for Kerberos-based authentication. Should only be set if protocol is "NFS_V4_1".
         */
        directoryServices?: Schema$DirectoryServicesConfig;
        /**
         * Server-specified ETag for the instance resource to prevent simultaneous updates from overwriting each other.
         */
        etag?: string | null;
        /**
         * File system shares on the instance. For this version, only a single file share is supported.
         */
        fileShares?: Schema$FileShareConfig[];
        /**
         * KMS key name used for data encryption.
         */
        kmsKeyName?: string | null;
        /**
         * Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The max capacity of the instance.
         */
        maxCapacityGb?: string | null;
        /**
         * The max number of shares allowed.
         */
        maxShareCount?: string | null;
        /**
         * Indicates whether this instance uses a multi-share configuration with which it can have more than one file-share or none at all. File-shares are added, updated and removed through the separate file-share APIs.
         */
        multiShareEnabled?: boolean | null;
        /**
         * Output only. The resource name of the instance, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}`.
         */
        name?: string | null;
        /**
         * VPC networks to which the instance is connected. For this version, only a single network is supported.
         */
        networks?: Schema$NetworkConfig[];
        /**
         * Immutable. The protocol indicates the access protocol for all shares in the instance. This field is immutable and it cannot be changed after the instance has been created. Default value: `NFS_V3`.
         */
        protocol?: string | null;
        /**
         * Optional. Replicaition configuration.
         */
        replication?: Schema$Replication;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzi?: boolean | null;
        /**
         * Output only. Reserved for future use.
         */
        satisfiesPzs?: boolean | null;
        /**
         * Output only. The instance state.
         */
        state?: string | null;
        /**
         * Output only. Additional information about the instance state, if available.
         */
        statusMessage?: string | null;
        /**
         * Output only. Field indicates all the reasons the instance is in "SUSPENDED" state.
         */
        suspensionReasons?: string[] | null;
        /**
         * Optional. Input only. Immutable. Tag keys/values directly bound to this resource. For example: "123/environment": "production", "123/costCenter": "marketing"
         */
        tags?: {
            [key: string]: string;
        } | null;
        /**
         * The service tier of the instance.
         */
        tier?: string | null;
    }
    /**
     * ListBackupsResponse is the result of ListBackupsRequest.
     */
    export interface Schema$ListBackupsResponse {
        /**
         * A list of backups in the project for the specified location. If the `{location\}` value in the request is "-", the response contains a list of backups from all locations. If any location is unreachable, the response will only return backups in reachable locations and the "unreachable" field will be populated with a list of unreachable locations.
         */
        backups?: Schema$Backup[];
        /**
         * The token you can use to retrieve the next page of results. Not returned if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * ListInstancesResponse is the result of ListInstancesRequest.
     */
    export interface Schema$ListInstancesResponse {
        /**
         * A list of instances in the project for the specified location. If the `{location\}` value in the request is "-", the response contains a list of instances from all locations. If any location is unreachable, the response will only return instances in reachable locations and the "unreachable" field will be populated with a list of unreachable locations.
         */
        instances?: Schema$Instance[];
        /**
         * The token you can use to retrieve the next page of results. Not returned if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * ListSharesResponse is the result of ListSharesRequest.
     */
    export interface Schema$ListSharesResponse {
        /**
         * The token you can use to retrieve the next page of results. Not returned if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of shares in the project for the specified instance.
         */
        shares?: Schema$Share[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * ListSnapshotsResponse is the result of ListSnapshotsRequest.
     */
    export interface Schema$ListSnapshotsResponse {
        /**
         * The token you can use to retrieve the next page of results. Not returned if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of snapshots in the project for the specified instance.
         */
        snapshots?: Schema$Snapshot[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * Defines policies to service maintenance events.
     */
    export interface Schema$MaintenancePolicy {
        /**
         * Output only. The time when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. Description of what this policy is for. Create/Update methods return INVALID_ARGUMENT if the length is greater than 512.
         */
        description?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata. Each label is a key-value pair, where both the key and the value are arbitrary strings provided by the user.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. MaintenancePolicy name using the form: `projects/{project_id\}/locations/{location_id\}/maintenancePolicies/{maintenance_policy_id\}` where {project_id\} refers to a GCP consumer project ID, {location_id\} refers to a GCP region/zone, {maintenance_policy_id\} must be 1-63 characters long and match the regular expression `[a-z0-9]([-a-z0-9]*[a-z0-9])?`.
         */
        name?: string | null;
        /**
         * Optional. The state of the policy.
         */
        state?: string | null;
        /**
         * Maintenance policy applicable to instance update.
         */
        updatePolicy?: Schema$UpdatePolicy;
        /**
         * Output only. The time when the resource was updated.
         */
        updateTime?: string | null;
    }
    /**
     * MaintenanceWindow definition.
     */
    export interface Schema$MaintenanceWindow {
        /**
         * Daily cycle.
         */
        dailyCycle?: Schema$DailyCycle;
        /**
         * Weekly cycle.
         */
        weeklyCycle?: Schema$WeeklyCycle;
    }
    /**
     * ManagedActiveDirectoryConfig contains all the parameters for connecting to Managed Active Directory.
     */
    export interface Schema$ManagedActiveDirectoryConfig {
        /**
         * Required. The computer name is used as a prefix to the mount remote target. Example: if the computer is `my-computer`, the mount command will look like: `$mount -o vers=4.1,sec=krb5 my-computer.filestore.: `.
         */
        computer?: string | null;
        /**
         * Required. The domain resource name, in the format `projects/{project_id\}/locations/global/domains/{domain\}`.
         */
        domain?: string | null;
    }
    /**
     * Network configuration for the instance.
     */
    export interface Schema$NetworkConfig {
        /**
         * The network connect mode of the Filestore instance. If not provided, the connect mode defaults to DIRECT_PEERING.
         */
        connectMode?: string | null;
        /**
         * Output only. IPv4 addresses in the format `{octet1\}.{octet2\}.{octet3\}.{octet4\}` or IPv6 addresses in the format `{block1\}:{block2\}:{block3\}:{block4\}:{block5\}:{block6\}:{block7\}:{block8\}`.
         */
        ipAddresses?: string[] | null;
        /**
         * Internet protocol versions for which the instance has IP addresses assigned. For this version, only MODE_IPV4 is supported.
         */
        modes?: string[] | null;
        /**
         * The name of the Google Compute Engine [VPC network](https://cloud.google.com/vpc/docs/vpc) to which the instance is connected.
         */
        network?: string | null;
        /**
         * Optional, reserved_ip_range can have one of the following two types of values. * CIDR range value when using DIRECT_PEERING connect mode. * [Allocated IP address range](https://cloud.google.com/compute/docs/ip-addresses/reserve-static-internal-ip-address) when using PRIVATE_SERVICE_ACCESS connect mode. When the name of an allocated IP address range is specified, it must be one of the ranges associated with the private service access connection. When specified as a direct CIDR value, it must be a /29 CIDR block for Basic tier, a /24 CIDR block for High Scale tier, or a /26 CIDR block for Enterprise tier in one of the [internal IP address ranges](https://www.arin.net/reference/research/statistics/address_filters/) that identifies the range of IP addresses reserved for this instance. For example, 10.0.0.0/29, ***********/24, or ***********/26, respectively. The range you specify can't overlap with either existing subnets or assigned IP address ranges for other Filestore instances in the selected VPC network.
         */
        reservedIpRange?: string | null;
    }
    /**
     * NFS export options specifications.
     */
    export interface Schema$NfsExportOptions {
        /**
         * Either READ_ONLY, for allowing only read requests on the exported directory, or READ_WRITE, for allowing both read and write requests. The default is READ_WRITE.
         */
        accessMode?: string | null;
        /**
         * An integer representing the anonymous group id with a default value of 65534. Anon_gid may only be set with squash_mode of ROOT_SQUASH. An error will be returned if this field is specified for other squash_mode settings.
         */
        anonGid?: string | null;
        /**
         * An integer representing the anonymous user id with a default value of 65534. Anon_uid may only be set with squash_mode of ROOT_SQUASH. An error will be returned if this field is specified for other squash_mode settings.
         */
        anonUid?: string | null;
        /**
         * List of either an IPv4 addresses in the format `{octet1\}.{octet2\}.{octet3\}.{octet4\}` or CIDR ranges in the format `{octet1\}.{octet2\}.{octet3\}.{octet4\}/{mask size\}` which may mount the file share. Overlapping IP ranges are not allowed, both within and across NfsExportOptions. An error will be returned. The limit is 64 IP ranges/addresses for each FileShareConfig among all NfsExportOptions.
         */
        ipRanges?: string[] | null;
        /**
         * The security flavors allowed for mount operations. The default is AUTH_SYS.
         */
        securityFlavors?: string[] | null;
        /**
         * Either NO_ROOT_SQUASH, for allowing root access on the exported directory, or ROOT_SQUASH, for not allowing root access. The default is NO_ROOT_SQUASH.
         */
        squashMode?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$OperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        cancelRequested?: boolean | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusDetail?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * PromoteReplicaRequest promotes a Filestore standby instance (replica).
     */
    export interface Schema$PromoteReplicaRequest {
    }
    /**
     * Replica configuration for the instance.
     */
    export interface Schema$ReplicaConfig {
        /**
         * Output only. The timestamp of the latest replication snapshot taken on the active instance and is already replicated safely.
         */
        lastActiveSyncTime?: string | null;
        /**
         * The peer instance.
         */
        peerInstance?: string | null;
        /**
         * Output only. The replica state.
         */
        state?: string | null;
        /**
         * Output only. Additional information about the replication state, if available.
         */
        stateReasons?: string[] | null;
    }
    /**
     * Replication specifications.
     */
    export interface Schema$Replication {
        /**
         * Replicas configuration on the instance. For now, only a single replica config is supported.
         */
        replicas?: Schema$ReplicaConfig[];
        /**
         * Output only. The replication role.
         */
        role?: string | null;
    }
    /**
     * RestoreInstanceRequest restores an existing instance's file share from a backup.
     */
    export interface Schema$RestoreInstanceRequest {
        /**
         * Required. Name of the file share in the Filestore instance that the backup is being restored to.
         */
        fileShare?: string | null;
        /**
         * The resource name of the backup, in the format `projects/{project_id\}/locations/{location_id\}/backups/{backup_id\}`.
         */
        sourceBackup?: string | null;
        /**
         * The resource name of the snapshot, in the format `projects/{project_id\}/locations/{location_id\}/snapshots/{snapshot_id\}`.
         */
        sourceSnapshot?: string | null;
    }
    /**
     * RevertInstanceRequest reverts the given instance's file share to the specified snapshot.
     */
    export interface Schema$RevertInstanceRequest {
        /**
         * Required. The snapshot resource ID, in the format 'my-snapshot', where the specified ID is the {snapshot_id\} of the fully qualified name like `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}/snapshots/{snapshot_id\}`
         */
        targetSnapshotId?: string | null;
    }
    /**
     * Configure the schedule.
     */
    export interface Schema$Schedule {
        /**
         * Allows to define schedule that runs specified day of the week.
         */
        day?: string | null;
        /**
         * Output only. Duration of the time window, set by service producer.
         */
        duration?: string | null;
        /**
         * Time within the window to start the operations.
         */
        startTime?: Schema$TimeOfDay;
    }
    /**
     * A Filestore share.
     */
    export interface Schema$Share {
        /**
         * Immutable. Full name of the Cloud Filestore Backup resource that this Share is restored from, in the format of projects/{project_id\}/locations/{location_id\}/backups/{backup_id\}. Empty, if the Share is created from scratch and not restored from a backup.
         */
        backup?: string | null;
        /**
         * File share capacity in gigabytes (GB). Filestore defines 1 GB as 1024^3 bytes. Must be greater than 0.
         */
        capacityGb?: string | null;
        /**
         * Output only. The time when the share was created.
         */
        createTime?: string | null;
        /**
         * A description of the share with 2048 characters or less. Requests with longer descriptions will be rejected.
         */
        description?: string | null;
        /**
         * Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The mount name of the share. Must be 63 characters or less and consist of uppercase or lowercase letters, numbers, and underscores.
         */
        mountName?: string | null;
        /**
         * Output only. The resource name of the share, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}/shares/{share_id\}`.
         */
        name?: string | null;
        /**
         * Nfs Export Options. There is a limit of 10 export options per file share.
         */
        nfsExportOptions?: Schema$NfsExportOptions[];
        /**
         * Output only. The share state.
         */
        state?: string | null;
    }
    /**
     * A Filestore snapshot.
     */
    export interface Schema$Snapshot {
        /**
         * Output only. The time when the snapshot was created.
         */
        createTime?: string | null;
        /**
         * A description of the snapshot with 2048 characters or less. Requests with longer descriptions will be rejected.
         */
        description?: string | null;
        /**
         * Output only. The amount of bytes needed to allocate a full copy of the snapshot content
         */
        filesystemUsedBytes?: string | null;
        /**
         * Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The resource name of the snapshot, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}/snapshots/{snapshot_id\}`.
         */
        name?: string | null;
        /**
         * Output only. The snapshot state.
         */
        state?: string | null;
        /**
         * Optional. Input only. Immutable. Tag keys/values directly bound to this resource. For example: "123/environment": "production", "123/costCenter": "marketing"
         */
        tags?: {
            [key: string]: string;
        } | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.
     */
    export interface Schema$TimeOfDay {
        /**
         * Hours of day in 24 hour format. Should be from 0 to 23. An API may choose to allow the value "24:00:00" for scenarios like business closing time.
         */
        hours?: number | null;
        /**
         * Minutes of hour of day. Must be from 0 to 59.
         */
        minutes?: number | null;
        /**
         * Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
         */
        nanos?: number | null;
        /**
         * Seconds of minutes of the time. Must normally be from 0 to 59. An API may allow the value 60 if it allows leap-seconds.
         */
        seconds?: number | null;
    }
    /**
     * Maintenance policy applicable to instance updates.
     */
    export interface Schema$UpdatePolicy {
        /**
         * Optional. Relative scheduling channel applied to resource.
         */
        channel?: string | null;
        /**
         * Deny Maintenance Period that is applied to resource to indicate when maintenance is forbidden. The protocol supports zero-to-many such periods, but the current SLM Rollout implementation only supports zero-to-one.
         */
        denyMaintenancePeriods?: Schema$DenyMaintenancePeriod[];
        /**
         * Optional. Maintenance window that is applied to resources covered by this policy.
         */
        window?: Schema$MaintenanceWindow;
    }
    /**
     * Time window specified for weekly operations.
     */
    export interface Schema$WeeklyCycle {
        /**
         * User can specify multiple windows in a week. Minimum of 1 window.
         */
        schedule?: Schema$Schedule[];
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        backups: Resource$Projects$Locations$Backups;
        instances: Resource$Projects$Locations$Instances;
        operations: Resource$Projects$Locations$Operations;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * If true, the returned list will include locations which are not yet revealed.
         */
        includeUnrevealedLocations?: boolean;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Backups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Backups$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Backups$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Backups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Backups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Backups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Backups$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Backups$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Backups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Backups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Backups$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the details of a specific backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Backups$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Backups$Get, options?: MethodOptions): GaxiosPromise<Schema$Backup>;
        get(params: Params$Resource$Projects$Locations$Backups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Backups$Get, options: MethodOptions | BodyResponseCallback<Schema$Backup>, callback: BodyResponseCallback<Schema$Backup>): void;
        get(params: Params$Resource$Projects$Locations$Backups$Get, callback: BodyResponseCallback<Schema$Backup>): void;
        get(callback: BodyResponseCallback<Schema$Backup>): void;
        /**
         * Lists all backups in a project for either a specified location or for all locations.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Backups$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Backups$List, options?: MethodOptions): GaxiosPromise<Schema$ListBackupsResponse>;
        list(params: Params$Resource$Projects$Locations$Backups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Backups$List, options: MethodOptions | BodyResponseCallback<Schema$ListBackupsResponse>, callback: BodyResponseCallback<Schema$ListBackupsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Backups$List, callback: BodyResponseCallback<Schema$ListBackupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListBackupsResponse>): void;
        /**
         * Updates the settings of a specific backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Backups$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Backups$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Backups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Backups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Backups$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Backups$Create extends StandardParameters {
        /**
         * Required. The ID to use for the backup. The ID must be unique within the specified project and location. This value must start with a lowercase letter followed by up to 62 lowercase letters, numbers, or hyphens, and cannot end with a hyphen.
         */
        backupId?: string;
        /**
         * Required. The backup's project and location, in the format `projects/{project_id\}/locations/{location\}`. In Filestore, backup locations map to Google Cloud regions, for example **us-west1**.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Backup;
    }
    export interface Params$Resource$Projects$Locations$Backups$Delete extends StandardParameters {
        /**
         * Required. The backup resource name, in the format `projects/{project_id\}/locations/{location\}/backups/{backup_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Backups$Get extends StandardParameters {
        /**
         * Required. The backup resource name, in the format `projects/{project_id\}/locations/{location\}/backups/{backup_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Backups$List extends StandardParameters {
        /**
         * List filter.
         */
        filter?: string;
        /**
         * Sort results. Supported values are "name", "name desc" or "" (unsorted).
         */
        orderBy?: string;
        /**
         * The maximum number of items to return.
         */
        pageSize?: number;
        /**
         * The next_page_token value to use if there are additional results to retrieve for this list request.
         */
        pageToken?: string;
        /**
         * Required. The project and location for which to retrieve backup information, in the format `projects/{project_id\}/locations/{location\}`. In Filestore, backup locations map to Google Cloud regions, for example **us-west1**. To retrieve backup information for all locations, use "-" for the `{location\}` value.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Backups$Patch extends StandardParameters {
        /**
         * Output only. The resource name of the backup, in the format `projects/{project_id\}/locations/{location_id\}/backups/{backup_id\}`.
         */
        name?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Backup;
    }
    export class Resource$Projects$Locations$Instances {
        context: APIRequestContext;
        shares: Resource$Projects$Locations$Instances$Shares;
        snapshots: Resource$Projects$Locations$Instances$Snapshots;
        constructor(context: APIRequestContext);
        /**
         * Creates an instance. When creating from a backup, the capacity of the new instance needs to be equal to or larger than the capacity of the backup (and also equal to or larger than the minimum capacity of the tier).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Instances$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Instances$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the details of a specific instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Instances$Get, options?: MethodOptions): GaxiosPromise<Schema$Instance>;
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Get, options: MethodOptions | BodyResponseCallback<Schema$Instance>, callback: BodyResponseCallback<Schema$Instance>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Get, callback: BodyResponseCallback<Schema$Instance>): void;
        get(callback: BodyResponseCallback<Schema$Instance>): void;
        /**
         * Lists all instances in a project for either a specified location or for all locations.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Instances$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Instances$List, options?: MethodOptions): GaxiosPromise<Schema$ListInstancesResponse>;
        list(params: Params$Resource$Projects$Locations$Instances$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Instances$List, options: MethodOptions | BodyResponseCallback<Schema$ListInstancesResponse>, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Instances$List, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        /**
         * Updates the settings of a specific instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Instances$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Instances$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Instances$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Instances$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Instances$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Promote an standby instance (replica).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        promoteReplica(params: Params$Resource$Projects$Locations$Instances$Promotereplica, options: StreamMethodOptions): GaxiosPromise<Readable>;
        promoteReplica(params?: Params$Resource$Projects$Locations$Instances$Promotereplica, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        promoteReplica(params: Params$Resource$Projects$Locations$Instances$Promotereplica, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        promoteReplica(params: Params$Resource$Projects$Locations$Instances$Promotereplica, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        promoteReplica(params: Params$Resource$Projects$Locations$Instances$Promotereplica, callback: BodyResponseCallback<Schema$Operation>): void;
        promoteReplica(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Restores an existing instance's file share from a backup. The capacity of the instance needs to be equal to or larger than the capacity of the backup (and also equal to or larger than the minimum capacity of the tier).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        restore(params: Params$Resource$Projects$Locations$Instances$Restore, options: StreamMethodOptions): GaxiosPromise<Readable>;
        restore(params?: Params$Resource$Projects$Locations$Instances$Restore, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        restore(params: Params$Resource$Projects$Locations$Instances$Restore, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        restore(params: Params$Resource$Projects$Locations$Instances$Restore, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        restore(params: Params$Resource$Projects$Locations$Instances$Restore, callback: BodyResponseCallback<Schema$Operation>): void;
        restore(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Revert an existing instance's file system to a specified snapshot.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        revert(params: Params$Resource$Projects$Locations$Instances$Revert, options: StreamMethodOptions): GaxiosPromise<Readable>;
        revert(params?: Params$Resource$Projects$Locations$Instances$Revert, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        revert(params: Params$Resource$Projects$Locations$Instances$Revert, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        revert(params: Params$Resource$Projects$Locations$Instances$Revert, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        revert(params: Params$Resource$Projects$Locations$Instances$Revert, callback: BodyResponseCallback<Schema$Operation>): void;
        revert(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Instances$Create extends StandardParameters {
        /**
         * Required. The ID of the instance to create. The ID must be unique within the specified project and location. This value must start with a lowercase letter followed by up to 62 lowercase letters, numbers, or hyphens, and cannot end with a hyphen.
         */
        instanceId?: string;
        /**
         * Required. The instance's project and location, in the format `projects/{project_id\}/locations/{location\}`. In Filestore, locations map to Google Cloud zones, for example **us-west1-b**.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Instance;
    }
    export interface Params$Resource$Projects$Locations$Instances$Delete extends StandardParameters {
        /**
         * If set to true, any snapshots of the instance will also be deleted. (Otherwise, the request will only work if the instance has no snapshots.)
         */
        force?: boolean;
        /**
         * Required. The instance resource name, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Get extends StandardParameters {
        /**
         * Required. The instance resource name, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$List extends StandardParameters {
        /**
         * List filter.
         */
        filter?: string;
        /**
         * Sort results. Supported values are "name", "name desc" or "" (unsorted).
         */
        orderBy?: string;
        /**
         * The maximum number of items to return.
         */
        pageSize?: number;
        /**
         * The next_page_token value to use if there are additional results to retrieve for this list request.
         */
        pageToken?: string;
        /**
         * Required. The project and location for which to retrieve instance information, in the format `projects/{project_id\}/locations/{location\}`. In Cloud Filestore, locations map to Google Cloud zones, for example **us-west1-b**. To retrieve instance information for all locations, use "-" for the `{location\}` value.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Patch extends StandardParameters {
        /**
         * Output only. The resource name of the instance, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}`.
         */
        name?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields: * "description" * "directory_services" * "file_shares" * "labels"
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Instance;
    }
    export interface Params$Resource$Projects$Locations$Instances$Promotereplica extends StandardParameters {
        /**
         * Required. The resource name of the instance, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$PromoteReplicaRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Restore extends StandardParameters {
        /**
         * Required. The resource name of the instance, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RestoreInstanceRequest;
    }
    export interface Params$Resource$Projects$Locations$Instances$Revert extends StandardParameters {
        /**
         * Required. The resource name of the instance, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}`.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RevertInstanceRequest;
    }
    export class Resource$Projects$Locations$Instances$Shares {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a share.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Instances$Shares$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Instances$Shares$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Instances$Shares$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Shares$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Shares$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a share.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Instances$Shares$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Instances$Shares$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Instances$Shares$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Shares$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Shares$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the details of a specific share.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Instances$Shares$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Instances$Shares$Get, options?: MethodOptions): GaxiosPromise<Schema$Share>;
        get(params: Params$Resource$Projects$Locations$Instances$Shares$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Shares$Get, options: MethodOptions | BodyResponseCallback<Schema$Share>, callback: BodyResponseCallback<Schema$Share>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Shares$Get, callback: BodyResponseCallback<Schema$Share>): void;
        get(callback: BodyResponseCallback<Schema$Share>): void;
        /**
         * Lists all shares for a specified instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Instances$Shares$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Instances$Shares$List, options?: MethodOptions): GaxiosPromise<Schema$ListSharesResponse>;
        list(params: Params$Resource$Projects$Locations$Instances$Shares$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Instances$Shares$List, options: MethodOptions | BodyResponseCallback<Schema$ListSharesResponse>, callback: BodyResponseCallback<Schema$ListSharesResponse>): void;
        list(params: Params$Resource$Projects$Locations$Instances$Shares$List, callback: BodyResponseCallback<Schema$ListSharesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSharesResponse>): void;
        /**
         * Updates the settings of a specific share.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Instances$Shares$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Instances$Shares$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Instances$Shares$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Instances$Shares$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Instances$Shares$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Instances$Shares$Create extends StandardParameters {
        /**
         * Required. The Filestore Instance to create the share for, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        parent?: string;
        /**
         * Required. The ID to use for the share. The ID must be unique within the specified instance. This value must start with a lowercase letter followed by up to 62 lowercase letters, numbers, or hyphens, and cannot end with a hyphen.
         */
        shareId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Share;
    }
    export interface Params$Resource$Projects$Locations$Instances$Shares$Delete extends StandardParameters {
        /**
         * Required. The share resource name, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}/share/{share_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Shares$Get extends StandardParameters {
        /**
         * Required. The share resource name, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}/shares/{share_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Shares$List extends StandardParameters {
        /**
         * List filter.
         */
        filter?: string;
        /**
         * Sort results. Supported values are "name", "name desc" or "" (unsorted).
         */
        orderBy?: string;
        /**
         * The maximum number of items to return.
         */
        pageSize?: number;
        /**
         * The next_page_token value to use if there are additional results to retrieve for this list request.
         */
        pageToken?: string;
        /**
         * Required. The instance for which to retrieve share information, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Shares$Patch extends StandardParameters {
        /**
         * Output only. The resource name of the share, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}/shares/{share_id\}`.
         */
        name?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields: * "description" * "capacity_gb" * "labels" * "nfs_export_options"
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Share;
    }
    export class Resource$Projects$Locations$Instances$Snapshots {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a snapshot.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Instances$Snapshots$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Instances$Snapshots$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Instances$Snapshots$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Snapshots$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Instances$Snapshots$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes a snapshot.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Instances$Snapshots$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Instances$Snapshots$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Instances$Snapshots$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Snapshots$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Instances$Snapshots$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the details of a specific snapshot.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Instances$Snapshots$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Instances$Snapshots$Get, options?: MethodOptions): GaxiosPromise<Schema$Snapshot>;
        get(params: Params$Resource$Projects$Locations$Instances$Snapshots$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Snapshots$Get, options: MethodOptions | BodyResponseCallback<Schema$Snapshot>, callback: BodyResponseCallback<Schema$Snapshot>): void;
        get(params: Params$Resource$Projects$Locations$Instances$Snapshots$Get, callback: BodyResponseCallback<Schema$Snapshot>): void;
        get(callback: BodyResponseCallback<Schema$Snapshot>): void;
        /**
         * Lists all snapshots in a project for either a specified location or for all locations.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Instances$Snapshots$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Instances$Snapshots$List, options?: MethodOptions): GaxiosPromise<Schema$ListSnapshotsResponse>;
        list(params: Params$Resource$Projects$Locations$Instances$Snapshots$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Instances$Snapshots$List, options: MethodOptions | BodyResponseCallback<Schema$ListSnapshotsResponse>, callback: BodyResponseCallback<Schema$ListSnapshotsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Instances$Snapshots$List, callback: BodyResponseCallback<Schema$ListSnapshotsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSnapshotsResponse>): void;
        /**
         * Updates the settings of a specific snapshot.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Instances$Snapshots$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Instances$Snapshots$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Instances$Snapshots$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Instances$Snapshots$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Instances$Snapshots$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Instances$Snapshots$Create extends StandardParameters {
        /**
         * Required. The Filestore Instance to create the snapshots of, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`
         */
        parent?: string;
        /**
         * Required. The ID to use for the snapshot. The ID must be unique within the specified instance. This value must start with a lowercase letter followed by up to 62 lowercase letters, numbers, or hyphens, and cannot end with a hyphen.
         */
        snapshotId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Snapshot;
    }
    export interface Params$Resource$Projects$Locations$Instances$Snapshots$Delete extends StandardParameters {
        /**
         * Required. The snapshot resource name, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}/snapshots/{snapshot_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Snapshots$Get extends StandardParameters {
        /**
         * Required. The snapshot resource name, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}/snapshots/{snapshot_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Snapshots$List extends StandardParameters {
        /**
         * List filter.
         */
        filter?: string;
        /**
         * Sort results. Supported values are "name", "name desc" or "" (unsorted).
         */
        orderBy?: string;
        /**
         * The maximum number of items to return.
         */
        pageSize?: number;
        /**
         * The next_page_token value to use if there are additional results to retrieve for this list request.
         */
        pageToken?: string;
        /**
         * Required. The instance for which to retrieve snapshot information, in the format `projects/{project_id\}/locations/{location\}/instances/{instance_id\}`.
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Instances$Snapshots$Patch extends StandardParameters {
        /**
         * Output only. The resource name of the snapshot, in the format `projects/{project_id\}/locations/{location_id\}/instances/{instance_id\}/snapshots/{snapshot_id\}`.
         */
        name?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Snapshot;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export {};
}
